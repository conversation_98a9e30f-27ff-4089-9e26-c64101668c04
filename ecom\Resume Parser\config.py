from pathlib import Path

# Project paths
PROJECT_ROOT = Path(__file__).parent
RESUMES_DIR = PROJECT_ROOT / "resumes"
OUTPUT_DIR = PROJECT_ROOT / "output"
TEMP_DIR = PROJECT_ROOT / "temp"

# Ollama configuration
OLLAMA_MODEL = "llama3.2:3b"
OLLAMA_TIMEOUT = 120  # seconds
OLLAMA_OPTIONS = {
    'temperature': 0.1,  # Low temperature for consistent scoring
    'top_p': 0.9,
    'num_predict': 1000,  # Max tokens to generate
}

# Processing configuration (sequential only)
# Multi-threading removed for Django integration

# Scoring configuration
MAX_SCORE = 100
MIN_SCORE = 0
PASSING_SCORE = 60

# Supported file formats
SUPPORTED_FORMATS = ['.pdf', '.docx', '.doc', '.txt']

# Resume parsing settings
MAX_FILE_SIZE_MB = 10
CHUNK_SIZE = 1000  # characters for LLM processing

# Output settings
OUTPUT_FORMATS = ['csv', 'json', 'xlsx']
DEFAULT_OUTPUT_FORMAT = 'csv'

# Create necessary directories
def create_directories():
    """Create necessary project directories if they don't exist"""
    directories = [RESUMES_DIR, OUTPUT_DIR, TEMP_DIR]
    for directory in directories:
        directory.mkdir(exist_ok=True)

# Scoring criteria weights (should sum to 1.0)
# Skills matching is now the dominant factor for real-time recruitment
SCORING_WEIGHTS = {
    'skills_match': 0.50,           # 50% - Technical skills alignment (CRITICAL)
    'experience_relevance': 0.20,   # 20% - Work experience relevance
    'education_match': 0.10,        # 10% - Educational background
    'keywords_match': 0.15,         # 15% - Job-specific keywords
    'overall_fit': 0.05             # 5% - General suitability
}

# Critical skills matching thresholds for real-time recruitment
CRITICAL_SKILLS_THRESHOLD = 60     # Minimum skills match score to be considered
MINIMUM_SKILLS_PERCENTAGE = 40     # Minimum % of required skills that must be present
SKILLS_VETO_THRESHOLD = 30         # Below this skills score = automatic REJECT
EXPERIENCE_COMPENSATION_LIMIT = 15  # Max points experience can add if skills are low

# Default job description template
DEFAULT_JOB_DESCRIPTION = """
Python & MySQL Developer - Fresher
📍 Location: Vijayawada, India
🕒 Job Type: Full-Time | Entry-Level
🌟 About the Role
We're looking for a passionate and inquisitive fresher eager to kick-start their journey in backend development and data-driven applications. If you're comfortable navigating Python’s data structures and dabbling in MySQL queries, this opportunity is your perfect launchpad!

🚀 Key Responsibilities
Build and manage backend logic using Python with a focus on clean data structures

Write efficient queries to interact with MySQL databases for CRUD operations

Collaborate with frontend developers to integrate APIs and ensure seamless data flow

Debug and optimize backend code for performance and scalability

Document processes and assist in deployment pipelines

🧠 Required Skills
Solid understanding of Python fundamentals including lists, dictionaries, sets, tuples, and OOP concepts

Familiarity with basic MySQL queries, joins, and indexing

Exposure to version control systems like Git

Good problem-solving and algorithmic thinking


🤝 What We Offer
Mentorship from senior developers

Real-world projects to build your portfolio

A friendly, collaborative work culture

Opportunities for growth into full-stack development roles
"""

# DEFAULT_JOB_DESCRIPTION = '''
# Software Developer Position - TechCorp Inc.

# We are seeking a skilled Software Developer to join our dynamic team.

# SKILLS:
# - 3+ years of experience in software development
# - Proficiency in Python, JavaScript, and SQL
# - Experience with web frameworks (Django, React, Node.js)
# - Knowledge of database systems (PostgreSQL, MongoDB)
# - Familiarity with cloud platforms (AWS, Azure)
# - Experience with version control (Git)
# - Understanding of Agile/Scrum methodologies

# QUALIFICATIONS:
# - Bachelor's degree in Computer Science or related field
# - Experience with containerization (Docker, Kubernetes)
# - Knowledge of CI/CD pipelines
# - Experience with microservices architecture
# - Strong problem-solving and communication skills

# RESPONSIBILITIES:
# - Develop and maintain web applications
# - Collaborate with cross-functional teams
# - Write clean, maintainable code
# - Participate in code reviews
# - Troubleshoot and debug applications
# - Stay updated with latest technologies

# BENEFITS:
# - Competitive salary
# - Health insurance
# - Remote work options
# - Professional development opportunities
# '''