"""
Django settings for ecommerce project.

Generated by 'django-admin startproject' using Django 3.0.5.

For more information on this file, see
https://docs.djangoproject.com/en/3.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.0/ref/settings/
"""

import os
from pathlib import Path

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
TEMPLATE_DIR = os.path.join(BASE_DIR,'templates')
STATIC_DIR=os.path.join(BASE_DIR,'static')


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = '#vw(03o=(9kbvg!&2d5i!2$_58x@_-3l4wujpow6(ym37jxnza'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

# ALLOWED_HOSTS = []
ALLOWED_HOSTS = [
    'localhost',
    '127.0.0.1',
    '2c8f-2409-40f0-3010-be04-35f0-b288-d31c-8563.ngrok-free.app',
]



# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'ecom',
    'widget_tweaks',
    'paypal.standard.ipn',
    'django_flatpickr',

]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',

]

ROOT_URLCONF = 'ecommerce.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [TEMPLATE_DIR,],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'ecommerce.wsgi.application'


# Database
# https://docs.djangoproject.com/en/3.0/ref/settings/#databases

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.sqlite3',
#         'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
#     }
# }

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "onlineshoppingprojectdjango",
        "USER": "root",
        "PASSWORD": "2004",
        "HOST": "127.0.0.1",
        "PORT": "3306",
    }
}


# Password validation
# https://docs.djangoproject.com/en/3.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/3.0/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_L10N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.0/howto/static-files/

STATIC_URL = '/static/'

STATICFILES_DIRS=[STATIC_DIR,]

# Media files configuration
MEDIA_URL = '/media/'
MEDIA_ROOT=os.path.join(BASE_DIR,'static')



LOGIN_REDIRECT_URL='/afterlogin'

#for contact us give your gmail id and password
EMAIL_BACKEND ='django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_USE_TLS = True
EMAIL_PORT = 587
EMAIL_HOST_USER = '<EMAIL>' # this email will be used to send emails
EMAIL_HOST_PASSWORD = 'xyz' # host email password required
# now sign in with your host gmail account in your browser
# open following link and turn it ON
# https://myaccount.google.com/lesssecureapps
# otherwise you will get SMTPAuthenticationError at /contactus
# this process is required because google blocks apps authentication by default
EMAIL_RECEIVING_USER = ['<EMAIL>'] # email on which you will receive messages sent from website


PAYPAL_RECEIVER_EMAIL = '<EMAIL>'

PAYPAL_TEST = True

HOST = 'http://127.0.0.1:8000/'


#   Resume Parse Config

from pathlib import Path

# Project paths
PROJECT_ROOT = Path(STATIC_DIR)
RESUME_PARSER_DIR = PROJECT_ROOT / "ResumeParser"
RESUMES_DIR = PROJECT_ROOT / "ResumeParser" / "resumes"
OUTPUT_DIR = PROJECT_ROOT / "ResumeParser" / "output"
TEMP_DIR = PROJECT_ROOT / "ResumeParser" / "temp"

# Ollama configuration
OLLAMA_MODEL = "llama3.2:3b"
OLLAMA_TIMEOUT = 120  # seconds
OLLAMA_OPTIONS = {
    'temperature': 0.1,  # Low temperature for consistent scoring
    'top_p': 0.9,
    'num_predict': 1000,  # Max tokens to generate
}

# Processing configuration (sequential only)
# Multi-threading removed for Django integration

# Scoring configuration
MAX_SCORE = 100
MIN_SCORE = 0
PASSING_SCORE = 60

# Supported file formats
SUPPORTED_FORMATS = ['.pdf', '.docx', '.doc', '.txt']

# Resume parsing settings
MAX_FILE_SIZE_MB = 10
CHUNK_SIZE = 1000  # characters for LLM processing

# Output settings
OUTPUT_FORMATS = ['csv', 'json', 'xlsx']
DEFAULT_OUTPUT_FORMAT = 'csv'
    

# Scoring criteria weights (should sum to 1.0)
# Skills matching is now the dominant factor for real-time recruitment
SCORING_WEIGHTS = {
    'skills_match': 0.50,           # 50% - Technical skills alignment (CRITICAL)
    'experience_relevance': 0.20,   # 20% - Work experience relevance
    'education_match': 0.10,        # 10% - Educational background
    'keywords_match': 0.15,         # 15% - Job-specific keywords
    'overall_fit': 0.05             # 5% - General suitability
}

# Critical skills matching thresholds for real-time recruitment
CRITICAL_SKILLS_THRESHOLD = 60     # Minimum skills match score to be considered
MINIMUM_SKILLS_PERCENTAGE = 40     # Minimum % of required skills that must be present
SKILLS_VETO_THRESHOLD = 30         # Below this skills score = automatic REJECT
EXPERIENCE_COMPENSATION_LIMIT = 15  # Max points experience can add if skills are low