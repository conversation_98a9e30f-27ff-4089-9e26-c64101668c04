{"meta": {"version": "1.0", "exported_at": "2025-07-12T07:17:33.811695", "total_candidates": 23, "successful_analyses": 23, "failed_analyses": 0, "job_description": "\nSenior Android Developer - Distributed Systems & Azure Automation\nLocation: Vijayawada / Remote\nType: Full-Time\nOverview:\nWe are hiring a developer with expertise in Android development, distributed systems architecture, and cloud automation using Azure Resource Manager. The role focuses exclusively on these three technical areas, emphasizing hands-on implementation and system design.\nResponsibilities:\n- Develop Android applications using best practices\n- Design and implement scalable distributed systems\n- Automate cloud infrastructure with Azure Resource Manager\nRequired Skills:\n- Android: Strong knowledge of Kotlin/Java, Android SDK, and UI development\n- Distributed Systems: Experience with system scalability, asynchronous processing, and fault tolerance\n- Azure Resource Manager: Proficiency in ARM templates and cloud resource provisioning\n", "processed_at": "2025-07-12T07:17:33.808718"}, "summary_statistics": {"average_score": 8.55, "score_distribution": {"excellent": 0, "good": 1, "average": 1, "below_average": 21}, "recommendations": {"HIRE": 1, "CONSIDER": 0, "REJECT": 22}, "processing_time": 115.85}, "candidates": [{"id": "resume_1", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 15.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required technical skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Gaps or weaknesses in candidate's profile"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 4.884435653686523, "processed_at": "2025-07-12T07:15:42.867882", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_2", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 5, "experience_score": 95.0, "education_score": 0, "keywords_match": 93.63636363636364, "overall_fit": 98.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills and relevant experience for the role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Senior Android Developer - Distributed Systems & Azure Automation"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Hands-on implementation and system design expertise", "Experience in deploying 90 of Googles products"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Indicators of cultural fit"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical implementation details", "System design and scalability"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.282994270324707, "processed_at": "2025-07-12T07:15:48.167547", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Senior Android Developer with expertise in Android development, distributed systems architecture, and cloud automation using Azure Resource Manager"}, {"id": "resume_3", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 85.0, "education_score": 0, "keywords_match": 85.45454545454545, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has relevant technical skills but lacks direct experience in Android development and distributed systems architecture.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Strong knowledge of Kotlin/Java, Android SDK, and UI development"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Previous experience with D.E. Shaw Co.", "Interned at Goldman Sachs as an SDE intern"], "weaknesses": ["Lack of direct experience in Android development", "No mention of distributed systems architecture or Azure automation"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of direct experience in Android development", "Technical skills vs. job requirements"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 6.289712905883789, "processed_at": "2025-07-12T07:15:54.488730", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": "<PERSON><PERSON><PERSON><PERSON>, SWE at Google with previous experience in D.E. Shaw Co. and Goldman Sachs."}, {"id": "resume_4", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 40.0, "keywords_match": 81.81818181818181, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON><PERSON><PERSON> has relevant software development experience, but lacks specific skills required for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior Android Developer role not mentioned in resume"], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong software development experience"], "weaknesses": ["Lack of direct experience with distributed systems and Azure Resource Manager"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of direct experience with distributed systems and Azure Resource Manager"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.13592267036438, "processed_at": "2025-07-12T07:15:59.634501", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": "Software development manager with 6 years of experience in Android and software development."}, {"id": "resume_5", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 63.4, "skills_match": 50.0, "experience_score": 95.0, "education_score": 0, "keywords_match": 100, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in Android development, distributed systems, and Azure automation.", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["Java", "Android"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure"], "skill_match_percentage": 50.0}, "experience_analysis": {"matching_experience": ["17 years in development of commercial software"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Data driven and lean startup practitioner"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Industry experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 4.887406826019287, "processed_at": "2025-07-12T07:16:04.553611", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of management experience and 17 years of development experience."}, {"id": "resume_6", "filename": "Profile_14.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 20.0, "education_score": 0, "keywords_match": 53.63636363636364, "overall_fit": 33.0, "growth_potential": 50.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for Senior Android Developer role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior Android Developer: Distributed Systems & Azure Automation"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 4.551215887069702, "processed_at": "2025-07-12T07:16:09.117913", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_14.pdf", "file_size": 0, "word_count": 218, "success": true, "error": null}, "summary": ""}, {"id": "resume_7", "filename": "Profile_15.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 95.0, "education_score": 60.0, "keywords_match": 92.27272727272727, "overall_fit": 88.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant experience in software design, but limited expertise in Azure Resource Manager.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": ["12.5 years of professional experience in Software Design Development"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.<PERSON><PERSON> (Electronics Telecommunications) Rank 6"], "education_level": "BASIC"}, "assessment": {"strengths": ["Customer focused individual", "Strong understanding of Core Java, Web Services (SOAP/REST) and various XML technologies"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Distributed Systems", "Android Development"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.350898027420044, "processed_at": "2025-07-12T07:16:14.483759", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_15.pdf", "file_size": 0, "word_count": 318, "success": true, "error": null}, "summary": "Customer focused individual with 12.5 years of professional experience in Software Design Development."}, {"id": "resume_8", "filename": "Profile_16.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 20.0, "keywords_match": 95.45454545454545, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical expertise, but limited Azure experience.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Amazon Director November 2017 - Present (7 years 8 months) Seattle Amazon.com Manager, Software Development August 2011 - Present (13 years 11 months) Learn and Save Senior Engineer October 2008 - February 2009 (5 months)"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Harvard University A.B., Computer Science  (1983 - 1997)"], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong leadership experience", "Technical expertise in Android and distributed systems"], "weaknesses": ["Limited Azure Resource Manager experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Azure Resource Manager experience", "Leadership skills"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.923229217529297, "processed_at": "2025-07-12T07:16:20.436395", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_16.pdf", "file_size": 0, "word_count": 120, "success": true, "error": null}, "summary": "Highly experienced software engineer with strong leadership skills and technical expertise in Android and distributed systems."}, {"id": "resume_9", "filename": "Profile_17.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 90.0, "education_score": 0, "keywords_match": 93.63636363636364, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in Android development and distributed systems, but limited Azure Resource Manager expertise.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": ["Software Engineering Manager", "Senior Software Development Engineer", "Software Development Engineer ll", "Software Development Engineer"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Azure Resource Manager", "Distributed Systems"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 4.798247337341309, "processed_at": "2025-07-12T07:16:25.243318", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_17.pdf", "file_size": 0, "word_count": 150, "success": true, "error": null}, "summary": "Experienced software engineer with 9 years of experience in Android development and distributed systems."}, {"id": "resume_10", "filename": "Profile_18.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 5.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior Android Developer - Distributed Systems & Azure Automation"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 4.466949939727783, "processed_at": "2025-07-12T07:16:29.717271", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_18.pdf", "file_size": 0, "word_count": 118, "success": true, "error": null}, "summary": ""}, {"id": "resume_11", "filename": "Profile_19.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 95.0, "education_score": 0, "keywords_match": 99.0909090909091, "overall_fit": 88.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills but limited experience in UI development and cloud resource provisioning.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Technical lead, Agile development"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Agile development"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.003918409347534, "processed_at": "2025-07-12T07:16:34.743883", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_19.pdf", "file_size": 0, "word_count": 546, "success": true, "error": null}, "summary": "Experienced programmer, architect, and technical lead with expertise in distributed systems, cloud native applications, and server-side development."}, {"id": "resume_12", "filename": "Profile_2.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 20.0, "keywords_match": 65.45454545454545, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "MID"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Gaps in required skills"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Required skills and experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 4.522868871688843, "processed_at": "2025-07-12T07:16:39.275359", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_2.pdf", "file_size": 0, "word_count": 119, "success": true, "error": null}, "summary": "Software Developer with experience in React Js and Web Development"}, {"id": "resume_13", "filename": "Profile_20.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 5.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Gaps or weaknesses in candidate's profile"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 4.80785059928894, "processed_at": "2025-07-12T07:16:44.095631", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_20.pdf", "file_size": 0, "word_count": 232, "success": true, "error": null}, "summary": "HR professional with 2.5 years of experience in IT and non IT industry"}, {"id": "resume_14", "filename": "Profile_21.pdf", "candidate_name": "Ka<PERSON>ya E.", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant technical experience and skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Easwari Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Gaps in technical skills"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills and experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 4.84835147857666, "processed_at": "2025-07-12T07:16:48.954504", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_21.pdf", "file_size": 0, "word_count": 169, "success": true, "error": null}, "summary": "HR Talent Acquisition professional with no clear technical background"}, {"id": "resume_15", "filename": "Profile_3.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required technical skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Gaps in technical skills"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 4.539927244186401, "processed_at": "2025-07-12T07:16:53.507396", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_3.pdf", "file_size": 0, "word_count": 289, "success": true, "error": null}, "summary": ""}, {"id": "resume_16", "filename": "Profile_4.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 25.0, "education_score": 0, "keywords_match": 0, "overall_fit": 33.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 4.3437340259552, "processed_at": "2025-07-12T07:16:57.868153", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_4.pdf", "file_size": 0, "word_count": 419, "success": true, "error": null}, "summary": ""}, {"id": "resume_17", "filename": "Profile_5.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 70.0, "keywords_match": 86.81818181818181, "overall_fit": 88.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills, but limited direct experience with Azure Resource Manager and Android development for distributed systems.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Develop Android applications using best practices", "Design and implement scalable distributed systems", "Automate cloud infrastructure with Azure Resource Manager"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "STANDARD"}, "assessment": {"strengths": ["Robust problem-solving", "Ability to manage high-volume data workloads", "Leadership and mentorship experience"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Leadership and mentorship experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.551966667175293, "processed_at": "2025-07-12T07:17:03.433231", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_5.pdf", "file_size": 0, "word_count": 290, "success": true, "error": null}, "summary": "Transformative journey as a Member of Technical Staff at Oracle, leveraging Apache Kafka and Kafka Streams in cutting-edge data processing projects."}, {"id": "resume_18", "filename": "Profile_6.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 0, "keywords_match": 95.45454545454545, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills and relevant experience, but limited Azure expertise.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Senior Software Engineer", "Samsung", "ISRO", "CodeChef"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Technical expertise, Leadership experience"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical questions", "Leadership experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.160794258117676, "processed_at": "2025-07-12T07:17:08.619243", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_6.pdf", "file_size": 0, "word_count": 524, "success": true, "error": null}, "summary": "Senior Software Engineer with expertise in Android development, distributed systems architecture, and cloud automation using Azure Resource Manager."}, {"id": "resume_19", "filename": "Profile_7.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 95.0, "education_score": 0, "keywords_match": 91.81818181818181, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks direct experience in Android UI development and distributed systems fault tolerance.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong foundation in data structures and algorithms", "Passionate competitive programmer"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Data structures", "Algorithms", "Competitive programming"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 4.982425212860107, "processed_at": "2025-07-12T07:17:13.626301", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_7.pdf", "file_size": 0, "word_count": 602, "success": true, "error": null}, "summary": "Passionate competitive programmer with strong foundation in data structures and algorithms."}, {"id": "resume_20", "filename": "Profile_8.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 55.0, "education_score": 20.0, "keywords_match": 79.0909090909091, "overall_fit": 58.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Skills mismatch with job requirements", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 4.312153577804565, "processed_at": "2025-07-12T07:17:17.957849", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_8.pdf", "file_size": 0, "word_count": 435, "success": true, "error": null}, "summary": ""}, {"id": "resume_21", "filename": "Profile_9.pdf", "candidate_name": "Top Skills", "scores": {"final_score": 85.25, "skills_match": 75.0, "experience_score": 100, "education_score": 80.0, "keywords_match": 100, "overall_fit": 95.0, "growth_potential": 95.0}, "recommendation": {"decision": "HIRE", "reason": "Strong technical skills and relevant experience, but limited job description match.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Java", "Azure", "Android"], "missing_skills": ["<PERSON><PERSON><PERSON>"], "skill_match_percentage": 75.0}, "experience_analysis": {"matching_experience": ["Senior Member of Technical Staff at Salesforce", "Software Development Engineer at Microsoft"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Electrical, Electronics, and Communications Engineering from PSG College of Technology"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Innovation", "Scalability"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Industry experience"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 4.7729551792144775, "processed_at": "2025-07-12T07:17:22.748763", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_9.pdf", "file_size": 0, "word_count": 383, "success": true, "error": null}, "summary": "Senior Android Developer with expertise in distributed systems and Azure automation."}, {"id": "resume_22", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 40.0, "education_score": 10.0, "keywords_match": 71.81818181818181, "overall_fit": 55.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has some relevant skills, but lacks experience and education in distributed systems and Azure Resource Manager.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Android"], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology"], "education_level": "BASIC"}, "assessment": {"strengths": ["Dedicated and passionate coder with a strong desire to excel", "Technologically adept, I offer experience with various social media platforms, office technology programs, and advanced computer skills"], "weaknesses": ["Gaps in relevant technical experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of relevant technical experience", "Education and skills gaps"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 6.105741024017334, "processed_at": "2025-07-12T07:17:28.895507", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Dedicated and passionate coder with a strong desire to excel. Technologically adept with various social media platforms and office technology programs experience."}, {"id": "resume_23", "filename": "resume_sasidhar.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 5, "experience_score": 25.0, "education_score": 0, "keywords_match": 59.09090909090909, "overall_fit": 38.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Gaps in technical expertise"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical expertise", "Relevant experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 4.868409156799316, "processed_at": "2025-07-12T07:17:33.808718", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume_sasidhar.pdf", "file_size": 0, "word_count": 578, "success": true, "error": null}, "summary": "Detail-oriented developer with hands-on experience in backend development and database integration"}], "top_candidates": [{"id": "resume_21", "filename": "Profile_9.pdf", "candidate_name": "Top Skills", "scores": {"final_score": 85.25, "skills_match": 75.0, "experience_score": 100, "education_score": 80.0, "keywords_match": 100, "overall_fit": 95.0, "growth_potential": 95.0}, "recommendation": {"decision": "HIRE", "reason": "Strong technical skills and relevant experience, but limited job description match.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Java", "Azure", "Android"], "missing_skills": ["<PERSON><PERSON><PERSON>"], "skill_match_percentage": 75.0}, "experience_analysis": {"matching_experience": ["Senior Member of Technical Staff at Salesforce", "Software Development Engineer at Microsoft"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Electrical, Electronics, and Communications Engineering from PSG College of Technology"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Innovation", "Scalability"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Industry experience"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 4.7729551792144775, "processed_at": "2025-07-12T07:17:22.748763", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_9.pdf", "file_size": 0, "word_count": 383, "success": true, "error": null}, "summary": "Senior Android Developer with expertise in distributed systems and Azure automation."}, {"id": "resume_5", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 63.4, "skills_match": 50.0, "experience_score": 95.0, "education_score": 0, "keywords_match": 100, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in Android development, distributed systems, and Azure automation.", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["Java", "Android"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure"], "skill_match_percentage": 50.0}, "experience_analysis": {"matching_experience": ["17 years in development of commercial software"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Data driven and lean startup practitioner"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Industry experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 4.887406826019287, "processed_at": "2025-07-12T07:16:04.553611", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of management experience and 17 years of development experience."}, {"id": "resume_3", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 85.0, "education_score": 0, "keywords_match": 85.45454545454545, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has relevant technical skills but lacks direct experience in Android development and distributed systems architecture.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Strong knowledge of Kotlin/Java, Android SDK, and UI development"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Previous experience with D.E. Shaw Co.", "Interned at Goldman Sachs as an SDE intern"], "weaknesses": ["Lack of direct experience in Android development", "No mention of distributed systems architecture or Azure automation"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of direct experience in Android development", "Technical skills vs. job requirements"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 6.289712905883789, "processed_at": "2025-07-12T07:15:54.488730", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": "<PERSON><PERSON><PERSON><PERSON>, SWE at Google with previous experience in D.E. Shaw Co. and Goldman Sachs."}, {"id": "resume_22", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 40.0, "education_score": 10.0, "keywords_match": 71.81818181818181, "overall_fit": 55.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has some relevant skills, but lacks experience and education in distributed systems and Azure Resource Manager.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Android"], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology"], "education_level": "BASIC"}, "assessment": {"strengths": ["Dedicated and passionate coder with a strong desire to excel", "Technologically adept, I offer experience with various social media platforms, office technology programs, and advanced computer skills"], "weaknesses": ["Gaps in relevant technical experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of relevant technical experience", "Education and skills gaps"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 6.105741024017334, "processed_at": "2025-07-12T07:17:28.895507", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Dedicated and passionate coder with a strong desire to excel. Technologically adept with various social media platforms and office technology programs experience."}, {"id": "resume_1", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 15.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required technical skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Gaps or weaknesses in candidate's profile"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 4.884435653686523, "processed_at": "2025-07-12T07:15:42.867882", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_2", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 5, "experience_score": 95.0, "education_score": 0, "keywords_match": 93.63636363636364, "overall_fit": 98.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills and relevant experience for the role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Senior Android Developer - Distributed Systems & Azure Automation"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Hands-on implementation and system design expertise", "Experience in deploying 90 of Googles products"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Indicators of cultural fit"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical implementation details", "System design and scalability"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.282994270324707, "processed_at": "2025-07-12T07:15:48.167547", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Senior Android Developer with expertise in Android development, distributed systems architecture, and cloud automation using Azure Resource Manager"}, {"id": "resume_4", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 40.0, "keywords_match": 81.81818181818181, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON><PERSON><PERSON> has relevant software development experience, but lacks specific skills required for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior Android Developer role not mentioned in resume"], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong software development experience"], "weaknesses": ["Lack of direct experience with distributed systems and Azure Resource Manager"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of direct experience with distributed systems and Azure Resource Manager"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.13592267036438, "processed_at": "2025-07-12T07:15:59.634501", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": "Software development manager with 6 years of experience in Android and software development."}, {"id": "resume_6", "filename": "Profile_14.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 20.0, "education_score": 0, "keywords_match": 53.63636363636364, "overall_fit": 33.0, "growth_potential": 50.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for Senior Android Developer role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior Android Developer: Distributed Systems & Azure Automation"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 4.551215887069702, "processed_at": "2025-07-12T07:16:09.117913", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_14.pdf", "file_size": 0, "word_count": 218, "success": true, "error": null}, "summary": ""}, {"id": "resume_7", "filename": "Profile_15.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 95.0, "education_score": 60.0, "keywords_match": 92.27272727272727, "overall_fit": 88.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant experience in software design, but limited expertise in Azure Resource Manager.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": ["12.5 years of professional experience in Software Design Development"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.<PERSON><PERSON> (Electronics Telecommunications) Rank 6"], "education_level": "BASIC"}, "assessment": {"strengths": ["Customer focused individual", "Strong understanding of Core Java, Web Services (SOAP/REST) and various XML technologies"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Distributed Systems", "Android Development"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.350898027420044, "processed_at": "2025-07-12T07:16:14.483759", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_15.pdf", "file_size": 0, "word_count": 318, "success": true, "error": null}, "summary": "Customer focused individual with 12.5 years of professional experience in Software Design Development."}, {"id": "resume_8", "filename": "Profile_16.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 20.0, "keywords_match": 95.45454545454545, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical expertise, but limited Azure experience.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Amazon Director November 2017 - Present (7 years 8 months) Seattle Amazon.com Manager, Software Development August 2011 - Present (13 years 11 months) Learn and Save Senior Engineer October 2008 - February 2009 (5 months)"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Harvard University A.B., Computer Science  (1983 - 1997)"], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong leadership experience", "Technical expertise in Android and distributed systems"], "weaknesses": ["Limited Azure Resource Manager experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Azure Resource Manager experience", "Leadership skills"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.923229217529297, "processed_at": "2025-07-12T07:16:20.436395", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_16.pdf", "file_size": 0, "word_count": 120, "success": true, "error": null}, "summary": "Highly experienced software engineer with strong leadership skills and technical expertise in Android and distributed systems."}]}