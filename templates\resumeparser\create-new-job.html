{% extends 'ecom/admin_base.html' %}
{% load static %}
{% load widget_tweaks %}
{% block content %}
<br>
<br><br><br><br><br>
<br><br><br><br><br>
<br><br><br>

    <form class="box" method="post" enctype="multipart/form-data">
        {% csrf_token %}
        <h1>Job Information</h1>

        {% render_field createjobform.job_code class="form-control" placeholder="Job Code" %}
        {% render_field createjobform.job_title class="form-control" placeholder="Job Title" %}
        {% render_field createjobform.job_description class="form-control" placeholder="Job Description" %}
        
        <input type="submit" value="Create">
    </form>
    <br><br><br><br><br>
    <br><br><br><br><br>
    <br><br><br><br><br>
    <br>

<style>

body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    height: 50vh !important;
}


    .box {
    width: 500px;
    padding: 40px;
    position: absolute;
    top: 40%;
    left: 50%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    text-align: center;
    transition: all 0.3s ease;
    margin-top: 100px;
    border-radius: 16px;
    box-shadow: 0 16px 48px rgba(26, 35, 126, 0.2);
    transform: translate(-50%, -50%);
}

.box input[type="text"],
.box input[type="password"],
.box input[type="file"] {
    border: 0;
    background: rgba(255, 255, 255, 0.9);
    display: block;
    margin: 15px auto;
    text-align: center;
    border: 2px solid #00acc1;
    padding: 12px 16px;
    width: 250px;
    outline: none;
    color: #212121;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.box h1 {
    color: #1a237e;
    text-transform: uppercase;
    font-weight: 700;
    margin-bottom: 10px;
}

.text-muted {
    color: #666666 !important;
    font-size: 14px;
}

.box input[type="text"]:focus,
.box input[type="password"]:focus,
.box input[type="file"]:focus {
    width: 300px;
    border-color: #1a237e;
    box-shadow: 0 0 0 3px rgba(26, 35, 126, 0.1);
    background: rgba(255, 255, 255, 1);
}

.box input[type="submit"] {
    border: 0;
    background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
    display: block;
    margin: 20px auto;
    text-align: center;
    padding: 14px 40px;
    outline: none;
    color: white;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    font-weight: 600;
    font-size: 16px;
    box-shadow: 0 4px 16px rgba(26, 35, 126, 0.3);
}

.box input[type="submit"]:hover {
    background: linear-gradient(135deg, #3949ab 0%, #5c6bc0 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(26, 35, 126, 0.4);
}
</style>


{% endblock content %}
