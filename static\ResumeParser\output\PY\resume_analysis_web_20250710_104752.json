{"meta": {"version": "1.0", "exported_at": "2025-07-10T10:47:52.398501", "total_candidates": 22, "successful_analyses": 22, "failed_analyses": 0, "job_description": "\nPython & MySQL Developer - Fresher\n📍 Location: Vijayawada, India\n🕒 Job Type: Full-Time | Entry-Level\n🌟 About the Role\nWe're looking for a passionate and inquisitive fresher eager to kick-start their journey in backend development and data-driven applications. If you're comfortable navigating Python’s data structures and dabbling in MySQL queries, this opportunity is your perfect launchpad!\n\n🚀 Key Responsibilities\nBuild and manage backend logic using Python with a focus on clean data structures\n\nWrite efficient queries to interact with MySQL databases for CRUD operations\n\nCollaborate with frontend developers to integrate APIs and ensure seamless data flow\n\nDebug and optimize backend code for performance and scalability\n\nDocument processes and assist in deployment pipelines\n\n🧠 Required Skills\nSolid understanding of Python fundamentals including lists, dictionaries, sets, tuples, and OOP concepts\n\nFamiliarity with basic MySQL queries, joins, and indexing\n\nExposure to version control systems like Git\n\nGood problem-solving and algorithmic thinking\n\n\n🤝 What We Offer\nMentorship from senior developers\n\nReal-world projects to build your portfolio\n\nA friendly, collaborative work culture\n\nOpportunities for growth into full-stack development roles\n", "processed_at": "2025-07-10T10:47:52.394858"}, "summary_statistics": {"average_score": 6.87, "score_distribution": {"excellent": 0, "good": 0, "average": 2, "below_average": 20}, "recommendations": {"HIRE": 0, "CONSIDER": 0, "REJECT": 22}, "processing_time": 78.56}, "candidates": [{"id": "resume_1", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 60.0, "keywords_match": 45.333333333333336, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks specific MySQL join experience and version control skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Software Development Engineer II", "Software Development Manager"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science", "Bachelor of Engineering (BE), Computer Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["MySQL joins", "Version control systems"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 22.61311674118042, "processed_at": "2025-07-10T10:47:52.242083", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": "Software Development Manager with 6 years of experience in Python and software development."}, {"id": "resume_2", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 45.0, "education_score": 0, "keywords_match": 21.333333333333332, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the job requirements", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["CRUD operations", "Collaboration with frontend developers", "Debugging and optimization", "Documentation and deployment pipelines"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 17.435199737548828, "processed_at": "2025-07-10T10:47:52.250614", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_3", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 62.38, "skills_match": 71.66666666666666, "experience_score": 85.0, "education_score": 0, "keywords_match": 39.333333333333336, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON>k of required skills and experience for the role.", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Exposure to version control systems like Git"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.832799196243286, "processed_at": "2025-07-10T10:47:52.265206", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": ""}, {"id": "resume_4", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 5, "experience_score": 95.0, "education_score": 0, "keywords_match": 68.0, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong backend development skills, but limited experience with OOP concepts and MySQL joins.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Backend development", "Data-driven applications"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 28.29908847808838, "processed_at": "2025-07-10T10:47:52.273882", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Experienced software engineer with a strong background in backend development and data-driven applications."}, {"id": "resume_5", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 85.0, "education_score": 0, "keywords_match": 86.66666666666667, "overall_fit": 78.0, "growth_potential": 85.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant experience in data-driven applications, but missing some required skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Version control systems"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Data driven and lean startup practitioner"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Data-driven applications", "Lean startup practices"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 23.446305751800537, "processed_at": "2025-07-10T10:47:52.287874", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of experience in management and 17 years in development of commercial software."}, {"id": "resume_6", "filename": "Profile_16.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 0, "keywords_match": 31.333333333333332, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["13 years 11 months of experience in software development"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["<PERSON><PERSON><PERSON><PERSON>, Computer Science from Harvard University"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.701820850372314, "processed_at": "2025-07-10T10:47:52.291886", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_16.pdf", "file_size": 0, "word_count": 120, "success": true, "error": null}, "summary": null}, {"id": "resume_7", "filename": "Profile_19.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 45.0, "education_score": 0, "keywords_match": 74.0, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Not suitable for the role due to lack of relevant skills and experience.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Solid understanding of Python fundamentals including lists, dictionaries, sets, tuples, and OOP concepts"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Proficient in building distributed, cloud native and server-side applications. Leading agile development teams. Author of Learning Boost C++ Libraries"], "weaknesses": ["Lack of experience with Python & MySQL"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of experience with Python & MySQL", "Insufficient knowledge of basic MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 18.237797021865845, "processed_at": "2025-07-10T10:47:52.301415", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_19.pdf", "file_size": 0, "word_count": 546, "success": true, "error": null}, "summary": "Experienced programmer, architect, and technical lead with expertise in distributed systems, cloud native applications, and server-side development."}, {"id": "resume_8", "filename": "Profile_17.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 0, "keywords_match": 11.333333333333332, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Not enough relevant skills and experience for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Software Development Engineer", "Senior Software Development Engineer"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Problem-solving skills", "Algorithmic thinking"], "weaknesses": ["Lack of experience in backend development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Python fundamentals"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.024747610092163, "processed_at": "2025-07-10T10:47:52.306414", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_17.pdf", "file_size": 0, "word_count": 150, "success": true, "error": null}, "summary": "Software engineer with 9 years of experience in AWS, but lacks backend development experience."}, {"id": "resume_9", "filename": "Profile_15.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 85.0, "education_score": 60.0, "keywords_match": 41.33333333333333, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has relevant experience but lacks specific skills for the job.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Enterprise Integration Architecture", "SOA and Microservices", "Agile environment"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.E. (Electronics Telecommunications)"], "education_level": "BASIC"}, "assessment": {"strengths": ["Customer focused individual", "Strong understanding of Core Java, Web Services (SOAP/REST) and various XML technologies"], "weaknesses": ["Lack of experience in backend development and data-driven applications"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Data-driven applications"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 19.24255394935608, "processed_at": "2025-07-10T10:47:52.313940", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_15.pdf", "file_size": 0, "word_count": 318, "success": true, "error": null}, "summary": "Customer focused individual with 12.5 years of professional experience in Software Design Development."}, {"id": "resume_10", "filename": "Profile_2.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 40.0, "keywords_match": 86.66666666666667, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has relevant experience and skills, but limited in backend development.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["React Js Developer", "Software Engineer <PERSON><PERSON>"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Bachelor of Technology - BTech, Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Problem-solving skills", "Algorithmic thinking"], "weaknesses": ["Limited experience in backend development"], "red_flags": [], "cultural_fit_indicators": ["Friendly, collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Backend development", "Problem-solving skills"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.759089469909668, "processed_at": "2025-07-10T10:47:52.317942", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_2.pdf", "file_size": 0, "word_count": 119, "success": true, "error": null}, "summary": "Fresh candidate with relevant experience and skills in React Js and software engineering."}, {"id": "resume_11", "filename": "Profile_14.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 40.0, "education_score": 0, "keywords_match": 11.333333333333332, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has some relevant training expertise but lacks direct experience in backend development and data-driven applications.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["NLP practitioner", "Corporate training experience"], "experience_gaps": ["No direct experience in backend development or data-driven applications"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["M-Technology Business Development Manager"], "education_level": "BASIC"}, "assessment": {"strengths": ["Training expertise", "Project management skills"], "weaknesses": ["Lack of relevant work experience in Python and MySQL"], "red_flags": [], "cultural_fit_indicators": ["Friendly, collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python fundamentals", "MySQL queries", "Backend development experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 19.699605703353882, "processed_at": "2025-07-10T10:47:52.323100", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_14.pdf", "file_size": 0, "word_count": 218, "success": true, "error": null}, "summary": "Freelance facilitator with 6+ years of experience in corporate training, campus placement training, and management training."}, {"id": "resume_12", "filename": "Profile_18.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 30.0}, "recommendation": {"decision": "REJECT", "reason": "Not a good fit for the role due to lack of relevant skills and experience.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Version control systems like Git"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor of Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": ["Senior Software Development Manager experience"], "weaknesses": ["Lack of relevant backend development experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development experience", "Python data structures"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.25369119644165, "processed_at": "2025-07-10T10:47:52.326627", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_18.pdf", "file_size": 0, "word_count": 118, "success": true, "error": null}, "summary": "Senior Software Development Manager with 6 years of experience in Amazon, but no backend development experience."}, {"id": "resume_13", "filename": "Profile_3.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 20.0, "education_score": 0, "keywords_match": 2.666666666666666, "overall_fit": 33.0, "growth_potential": 60.0}, "recommendation": {"decision": "REJECT", "reason": "Not suitable for Python & MySQL Developer role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Backend development experience"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Graphic design expertise"], "weaknesses": ["Lack of backend development experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development experience", "Python data structures"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 18.923880338668823, "processed_at": "2025-07-10T10:47:52.333161", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_3.pdf", "file_size": 0, "word_count": 289, "success": true, "error": null}, "summary": "Graphic designer with 5 years experience in Fiverr"}, {"id": "resume_14", "filename": "Profile_5.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 70.0, "keywords_match": 44.0, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical background, but limited experience in backend development.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Problem-solving", "Algorithmic thinking"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.Tech in Electronics Engineering from IIT BHU"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Leadership", "Mentorship"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Data processing"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.19664216041565, "processed_at": "2025-07-10T10:47:52.338679", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_5.pdf", "file_size": 0, "word_count": 290, "success": true, "error": null}, "summary": "Transformative journey as a Member of Technical Staff at Oracle."}, {"id": "resume_15", "filename": "Profile_4.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 45.0, "education_score": 20.0, "keywords_match": 22.666666666666664, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Not enough relevant skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Backend development experience"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Lack of backend development experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.85228729248047, "processed_at": "2025-07-10T10:47:52.346687", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_4.pdf", "file_size": 0, "word_count": 419, "success": true, "error": null}, "summary": ""}, {"id": "resume_16", "filename": "Profile_20.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 70.0, "education_score": 30.0, "keywords_match": 40.0, "overall_fit": 58.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant HR experience, but lacks backend development skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Human Resources experience"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["HR skills", "Recruitment experience"], "weaknesses": ["Lack of backend development experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development skills", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.30913519859314, "processed_at": "2025-07-10T10:47:52.351217", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_20.pdf", "file_size": 0, "word_count": 232, "success": true, "error": null}, "summary": "HR professional with 2.5 years of experience in IT and non IT industry."}, {"id": "resume_17", "filename": "Profile_21.pdf", "candidate_name": "Ka<PERSON>ya E.", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 70.0, "education_score": 0, "keywords_match": 20.0, "overall_fit": 58.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Strong HR background, but limited relevant technical skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["IT hiring experience", "Recruiting strategies", "Sourcing talent"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Easwari Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": ["HR Talent Acquisition expertise"], "weaknesses": ["Lack of backend development experience"], "red_flags": [], "cultural_fit_indicators": ["Collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills", "Recruiting strategies"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.480963945388794, "processed_at": "2025-07-10T10:47:52.356218", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_21.pdf", "file_size": 0, "word_count": 169, "success": true, "error": null}, "summary": "HR Talent Acquisition professional with IT hiring experience and recruiting expertise."}, {"id": "resume_18", "filename": "Profile_6.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 70.0, "keywords_match": 46.666666666666664, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks experience in clean data structures and algorithmic thinking.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["National Talent Search Examination", "Finalist at American Express Codestreet 20 Hackathon"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Generative AI", "Data"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Clean data structures", "Algorithmic thinking"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.485899686813354, "processed_at": "2025-07-10T10:47:52.364820", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_6.pdf", "file_size": 0, "word_count": 524, "success": true, "error": null}, "summary": "Senior Software Engineer with expertise in Generative AI, Data, and Technical Advocacy."}, {"id": "resume_19", "filename": "Profile_7.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 95.0, "education_score": 60.0, "keywords_match": 77.66666666666667, "overall_fit": 85.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong foundation in data structures and algorithms, but limited experience in backend development.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong foundation in data structures and algorithms", "Passionate competitive programmer"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Data structures", "Algorithms"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.957130670547485, "processed_at": "2025-07-10T10:47:52.374483", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_7.pdf", "file_size": 0, "word_count": 602, "success": true, "error": null}, "summary": "Passionate competitive programmer with a strong foundation in data structures and algorithms."}, {"id": "resume_20", "filename": "Profile_8.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 25.0, "education_score": 10.0, "keywords_match": 36.666666666666664, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Limited relevant skills and experience for the role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Version control systems like Git"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Programming Competition CODIFICTION"], "education_level": "BASIC"}, "assessment": {"strengths": ["Good problem-solving and algorithmic thinking"], "weaknesses": ["Lack of experience in backend development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.295559644699097, "processed_at": "2025-07-10T10:47:52.382173", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_8.pdf", "file_size": 0, "word_count": 435, "success": true, "error": null}, "summary": "Freshers with passion for backend development and data-driven applications"}, {"id": "resume_21", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 62.03, "skills_match": 71.66666666666666, "experience_score": 50.0, "education_score": 30.0, "keywords_match": 68.0, "overall_fit": 60.0, "growth_potential": 65.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks relevant experience and skills for the role", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.157765865325928, "processed_at": "2025-07-10T10:47:52.387766", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Dedicated and passionate coder with a strong desire to excel"}, {"id": "resume_22", "filename": "Profile_9.pdf", "candidate_name": "Top Skills", "scores": {"final_score": 26.67, "skills_match": 33.33333333333333, "experience_score": 85.0, "education_score": 0, "keywords_match": 68.0, "overall_fit": 68.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant experience and skills, but limited fit for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python"], "missing_skills": ["MySQL", "Git"], "skill_match_percentage": 33.3}, "experience_analysis": {"matching_experience": ["Ex- Microsoft", "Ex - Amazon"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Innovation", "Scalability"], "weaknesses": ["Lack of experience in backend development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Python fundamentals"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.421897649765015, "processed_at": "2025-07-10T10:47:52.394858", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_9.pdf", "file_size": 0, "word_count": 383, "success": true, "error": null}, "summary": "Software Engineer with experience in Android and distributed systems."}], "top_candidates": [{"id": "resume_3", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 62.38, "skills_match": 71.66666666666666, "experience_score": 85.0, "education_score": 0, "keywords_match": 39.333333333333336, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON>k of required skills and experience for the role.", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Exposure to version control systems like Git"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.832799196243286, "processed_at": "2025-07-10T10:47:52.265206", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": ""}, {"id": "resume_21", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 62.03, "skills_match": 71.66666666666666, "experience_score": 50.0, "education_score": 30.0, "keywords_match": 68.0, "overall_fit": 60.0, "growth_potential": 65.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks relevant experience and skills for the role", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.157765865325928, "processed_at": "2025-07-10T10:47:52.387766", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Dedicated and passionate coder with a strong desire to excel"}, {"id": "resume_22", "filename": "Profile_9.pdf", "candidate_name": "Top Skills", "scores": {"final_score": 26.67, "skills_match": 33.33333333333333, "experience_score": 85.0, "education_score": 0, "keywords_match": 68.0, "overall_fit": 68.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant experience and skills, but limited fit for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python"], "missing_skills": ["MySQL", "Git"], "skill_match_percentage": 33.3}, "experience_analysis": {"matching_experience": ["Ex- Microsoft", "Ex - Amazon"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Innovation", "Scalability"], "weaknesses": ["Lack of experience in backend development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Python fundamentals"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.421897649765015, "processed_at": "2025-07-10T10:47:52.394858", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_9.pdf", "file_size": 0, "word_count": 383, "success": true, "error": null}, "summary": "Software Engineer with experience in Android and distributed systems."}, {"id": "resume_1", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 60.0, "keywords_match": 45.333333333333336, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks specific MySQL join experience and version control skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Software Development Engineer II", "Software Development Manager"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science", "Bachelor of Engineering (BE), Computer Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["MySQL joins", "Version control systems"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 22.61311674118042, "processed_at": "2025-07-10T10:47:52.242083", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": "Software Development Manager with 6 years of experience in Python and software development."}, {"id": "resume_2", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 45.0, "education_score": 0, "keywords_match": 21.333333333333332, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the job requirements", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["CRUD operations", "Collaboration with frontend developers", "Debugging and optimization", "Documentation and deployment pipelines"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 17.435199737548828, "processed_at": "2025-07-10T10:47:52.250614", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_4", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 5, "experience_score": 95.0, "education_score": 0, "keywords_match": 68.0, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong backend development skills, but limited experience with OOP concepts and MySQL joins.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Backend development", "Data-driven applications"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 28.29908847808838, "processed_at": "2025-07-10T10:47:52.273882", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Experienced software engineer with a strong background in backend development and data-driven applications."}, {"id": "resume_5", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 85.0, "education_score": 0, "keywords_match": 86.66666666666667, "overall_fit": 78.0, "growth_potential": 85.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant experience in data-driven applications, but missing some required skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Version control systems"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Data driven and lean startup practitioner"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Data-driven applications", "Lean startup practices"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 23.446305751800537, "processed_at": "2025-07-10T10:47:52.287874", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of experience in management and 17 years in development of commercial software."}, {"id": "resume_6", "filename": "Profile_16.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 0, "keywords_match": 31.333333333333332, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["13 years 11 months of experience in software development"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["<PERSON><PERSON><PERSON><PERSON>, Computer Science from Harvard University"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.701820850372314, "processed_at": "2025-07-10T10:47:52.291886", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_16.pdf", "file_size": 0, "word_count": 120, "success": true, "error": null}, "summary": null}, {"id": "resume_7", "filename": "Profile_19.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 45.0, "education_score": 0, "keywords_match": 74.0, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Not suitable for the role due to lack of relevant skills and experience.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Solid understanding of Python fundamentals including lists, dictionaries, sets, tuples, and OOP concepts"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Proficient in building distributed, cloud native and server-side applications. Leading agile development teams. Author of Learning Boost C++ Libraries"], "weaknesses": ["Lack of experience with Python & MySQL"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of experience with Python & MySQL", "Insufficient knowledge of basic MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 18.237797021865845, "processed_at": "2025-07-10T10:47:52.301415", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_19.pdf", "file_size": 0, "word_count": 546, "success": true, "error": null}, "summary": "Experienced programmer, architect, and technical lead with expertise in distributed systems, cloud native applications, and server-side development."}, {"id": "resume_8", "filename": "Profile_17.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 0, "keywords_match": 11.333333333333332, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Not enough relevant skills and experience for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Software Development Engineer", "Senior Software Development Engineer"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Problem-solving skills", "Algorithmic thinking"], "weaknesses": ["Lack of experience in backend development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Python fundamentals"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.024747610092163, "processed_at": "2025-07-10T10:47:52.306414", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_17.pdf", "file_size": 0, "word_count": 150, "success": true, "error": null}, "summary": "Software engineer with 9 years of experience in AWS, but lacks backend development experience."}]}