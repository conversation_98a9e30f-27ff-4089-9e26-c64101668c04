{"meta": {"version": "1.0", "exported_at": "2025-07-10T14:31:28.028494", "total_candidates": 23, "successful_analyses": 23, "failed_analyses": 0, "job_description": "\nPython & MySQL Developer - Fresher\n📍 Location: Vijayawada, India\n🕒 Job Type: Full-Time | Entry-Level\n🌟 About the Role\nWe're looking for a passionate and inquisitive fresher eager to kick-start their journey in backend development and data-driven applications. If you're comfortable navigating Python’s data structures and dabbling in MySQL queries, this opportunity is your perfect launchpad!\n\n🚀 Key Responsibilities\nBuild and manage backend logic using Python with a focus on clean data structures\n\nWrite efficient queries to interact with MySQL databases for CRUD operations\n\nCollaborate with frontend developers to integrate APIs and ensure seamless data flow\n\nDebug and optimize backend code for performance and scalability\n\nDocument processes and assist in deployment pipelines\n\n🧠 Required Skills\nSolid understanding of Python fundamentals including lists, dictionaries, sets, tuples, and OOP concepts\n\nFamiliarity with basic MySQL queries, joins, and indexing\n\nExposure to version control systems like Git\n\nGood problem-solving and algorithmic thinking\n\n\n🤝 What We Offer\nMentorship from senior developers\n\nReal-world projects to build your portfolio\n\nA friendly, collaborative work culture\n\nOpportunities for growth into full-stack development roles\n", "processed_at": "2025-07-10T14:31:28.025489"}, "summary_statistics": {"average_score": 10.31, "score_distribution": {"excellent": 0, "good": 1, "average": 1, "below_average": 21}, "recommendations": {"HIRE": 1, "CONSIDER": 0, "REJECT": 22}, "processing_time": 66.74}, "candidates": [{"id": "resume_1", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 5, "experience_score": 95.0, "education_score": 0, "keywords_match": 93.0, "overall_fit": 95.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in backend development and data-driven applications, but missing some required skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Backend development", "Data-driven applications", "Version control systems"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Problem-solving, Algorithmic thinking"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 6.258759498596191, "processed_at": "2025-07-10T14:31:27.842273", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Experienced software engineer with a strong background in backend development and problem-solving."}, {"id": "resume_2", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 60.0, "keywords_match": 45.333333333333336, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks specific MySQL skills and experience in backend development.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Software Development Engineer II", "Software Development Manager"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science", "Bachelor of Engineering (BE), Computer Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["MySQL queries", "Backend development"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.984562873840332, "processed_at": "2025-07-10T14:31:27.848265", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": "Software Development Manager with 6 years of experience in Amazon."}, {"id": "resume_3", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 25.0, "education_score": 0, "keywords_match": 1.3333333333333321, "overall_fit": 53.0, "growth_potential": 40.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.229778051376343, "processed_at": "2025-07-10T14:31:27.858271", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_4", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 95.0, "education_score": 0, "keywords_match": 66.66666666666667, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience and skills, but limited education match.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["17 years of development experience", "Commercial software development"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Data driven", "Lean startup practitioner"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Commercial software development", "Lean startup practices"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.124152421951294, "processed_at": "2025-07-10T14:31:27.875265", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of management experience and 17 years of development experience."}, {"id": "resume_5", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 59.38, "skills_match": 71.66666666666666, "experience_score": 85.0, "education_score": 0, "keywords_match": 19.333333333333332, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON>k of required skills and experience for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Exposure to version control systems like Git"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.135844707489014, "processed_at": "2025-07-10T14:31:27.892263", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": ""}, {"id": "resume_6", "filename": "Profile_14.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 40.0, "education_score": 0, "keywords_match": 11.333333333333332, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant training expertise and project management skills, but lack of backend development experience.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["NLP practitioner", "Corporate training experience"], "experience_gaps": ["No backend development experience"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["M-Technology Business Development Manager"], "education_level": "BASIC"}, "assessment": {"strengths": ["Training expertise", "Project management skills"], "weaknesses": ["Lack of relevant work experience"], "red_flags": [], "cultural_fit_indicators": ["Friendly, collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of relevant work experience", "Backend development skills"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.406545877456665, "processed_at": "2025-07-10T14:31:27.898263", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_14.pdf", "file_size": 0, "word_count": 218, "success": true, "error": null}, "summary": "Freelance facilitator with 6+ years of corporate training experience and M-Technology Business Development Manager background."}, {"id": "resume_7", "filename": "Profile_15.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 85.0, "education_score": 90.0, "keywords_match": 41.33333333333333, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills, but limited experience in Python and MySQL.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Enterprise Integration Architecture", "SOA and Microservices", "Agile environment"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.E. (Electronics Telecommunications)"], "education_level": "ADVANCED"}, "assessment": {"strengths": ["Customer focused individual", "Strong understanding of Core Java, Web Services (SOAP/REST)"], "weaknesses": ["Lack of experience in backend development and data-driven applications"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.339215993881226, "processed_at": "2025-07-10T14:31:27.905263", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_15.pdf", "file_size": 0, "word_count": 318, "success": true, "error": null}, "summary": "Customer focused individual with strong understanding of Core Java and Web Services."}, {"id": "resume_8", "filename": "Profile_17.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 0, "keywords_match": 31.333333333333332, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Not enough relevant experience for a junior position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["9 years experience in software engineering"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong problem-solving skills"], "weaknesses": ["Lack of relevant backend development experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development experience", "Problem-solving skills"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.69105863571167, "processed_at": "2025-07-10T14:31:27.910265", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_17.pdf", "file_size": 0, "word_count": 150, "success": true, "error": null}, "summary": "Experienced software engineer with strong problem-solving skills but limited backend development experience"}, {"id": "resume_9", "filename": "Profile_16.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 20.0, "keywords_match": 61.33333333333333, "overall_fit": 68.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Overqualified for entry-level position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Software Development experience"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Harvard University A.B., Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Leadership skills", "Technical expertise"], "weaknesses": ["<PERSON>k of recent experience in backend development and data-driven applications"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Transitioning to backend development", "Data-driven applications"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.444708347320557, "processed_at": "2025-07-10T14:31:27.915270", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_16.pdf", "file_size": 0, "word_count": 120, "success": true, "error": null}, "summary": "Highly experienced software engineer with leadership skills and technical expertise."}, {"id": "resume_10", "filename": "Profile_18.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 30.0}, "recommendation": {"decision": "REJECT", "reason": "Not a good fit for the role due to lack of relevant experience and skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Version control systems like Git"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor of Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Lack of relevant experience in backend development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.696958065032959, "processed_at": "2025-07-10T14:31:27.921268", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_18.pdf", "file_size": 0, "word_count": 118, "success": true, "error": null}, "summary": ""}, {"id": "resume_11", "filename": "Profile_19.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 45.0, "education_score": 0, "keywords_match": 74.0, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Not suitable for the role due to lack of relevant skills and experience.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["No relevant experience in backend development and data-driven applications"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Proficient in building distributed, cloud native and server-side applications"], "weaknesses": ["Lack of experience in Python and MySQL"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python", "MySQL"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.433864116668701, "processed_at": "2025-07-10T14:31:27.934271", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_19.pdf", "file_size": 0, "word_count": 546, "success": true, "error": null}, "summary": ""}, {"id": "resume_12", "filename": "Profile_2.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 90.0, "keywords_match": 46.666666666666664, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON><PERSON> required skills for the job, but has potential with training.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["React Js Developer", "Software Engineer <PERSON><PERSON>"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Bachelor of Technology - BTech, Computer Science"], "education_level": "ADVANCED"}, "assessment": {"strengths": ["Problem-solving skills", "Algorithmic thinking"], "weaknesses": ["Limited experience in backend development"], "red_flags": [], "cultural_fit_indicators": ["Friendly work culture"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.333976984024048, "processed_at": "2025-07-10T14:31:27.939271", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_2.pdf", "file_size": 0, "word_count": 119, "success": true, "error": null}, "summary": "Freshers with experience in React Js and software engineering internships."}, {"id": "resume_13", "filename": "Profile_20.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 70.0, "education_score": 30.0, "keywords_match": 40.0, "overall_fit": 58.0, "growth_potential": 65.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant HR experience, but lacks backend development skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Human Resources experience"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["HR professional experience"], "education_level": "BASIC"}, "assessment": {"strengths": ["Fast-paced environment skills", "E2E Recruitment skills"], "weaknesses": ["Lack of backend development experience"], "red_flags": [], "cultural_fit_indicators": ["Collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development skills", "HR experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.112974643707275, "processed_at": "2025-07-10T14:31:27.946266", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_20.pdf", "file_size": 0, "word_count": 232, "success": true, "error": null}, "summary": "HR professional with 2.5 years of IT and non-IT industry experience."}, {"id": "resume_14", "filename": "Profile_21.pdf", "candidate_name": "Ka<PERSON>ya E.", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 0, "keywords_match": 10.0, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Candi<PERSON> has relevant HR experience but lacks backend development skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["IT hiring experience", "Recruiting strategies implementation"], "experience_gaps": ["Lack of backend development experience"], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Easwari Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": ["HR Talent Acquisition expertise", "Experience in head hunting and sourcing"], "weaknesses": ["Limited Python and MySQL skills"], "red_flags": [], "cultural_fit_indicators": ["Talent Acquisition experience"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.307555913925171, "processed_at": "2025-07-10T14:31:27.951270", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_21.pdf", "file_size": 0, "word_count": 169, "success": true, "error": null}, "summary": "HR Talent Acquisition professional with IT hiring experience and recruiting strategies implementation."}, {"id": "resume_15", "filename": "Profile_3.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 20.0, "education_score": 0, "keywords_match": 2.666666666666666, "overall_fit": 33.0, "growth_potential": 50.0}, "recommendation": {"decision": "REJECT", "reason": "Not enough relevant experience for the role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Backend development experience", "Data-driven applications experience"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Lack of relevant work experience in backend development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.49253487586975, "processed_at": "2025-07-10T14:31:27.957271", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_3.pdf", "file_size": 0, "word_count": 289, "success": true, "error": null}, "summary": ""}, {"id": "resume_16", "filename": "Profile_5.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 70.0, "keywords_match": 74.0, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience with Apache Kafka and problem-solving skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.Tech in Electronics Engineering from IIT BHU"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Robust problem-solving", "High-volume data workloads"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Data structures", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.42787766456604, "processed_at": "2025-07-10T14:31:27.963270", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_5.pdf", "file_size": 0, "word_count": 290, "success": true, "error": null}, "summary": "Transformative journey in data processing projects at Oracle."}, {"id": "resume_17", "filename": "Profile_4.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 45.0, "education_score": 0, "keywords_match": 12.666666666666666, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Not enough relevant experience for the role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Backend development experience"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Lack of backend development experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.21881365776062, "processed_at": "2025-07-10T14:31:27.971271", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_4.pdf", "file_size": 0, "word_count": 419, "success": true, "error": null}, "summary": ""}, {"id": "resume_18", "filename": "Profile_7.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 95.0, "education_score": 0, "keywords_match": 92.66666666666667, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong foundation in data structures and algorithms, but limited experience in backend development.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong foundation in data structures and algorithms", "Passionate competitive programmer"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.445834398269653, "processed_at": "2025-07-10T14:31:27.982270", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_7.pdf", "file_size": 0, "word_count": 602, "success": true, "error": null}, "summary": "Passionate competitive programmer with a strong foundation in data structures and algorithms."}, {"id": "resume_19", "filename": "Profile_9.pdf", "candidate_name": "Top Skills", "scores": {"final_score": 26.67, "skills_match": 33.33333333333333, "experience_score": 85.0, "education_score": 0, "keywords_match": 58.0, "overall_fit": 78.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant experience and skills, but limited fit for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python"], "missing_skills": ["MySQL", "Git"], "skill_match_percentage": 33.3}, "experience_analysis": {"matching_experience": ["Ex- Microsoft", "Ex- Amazon"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Innovation", "Scalability"], "weaknesses": ["Lack of experience in backend development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Python fundamentals"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.852697372436523, "processed_at": "2025-07-10T14:31:27.990270", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_9.pdf", "file_size": 0, "word_count": 383, "success": true, "error": null}, "summary": "Software Engineer with experience in Android and distributed systems."}, {"id": "resume_20", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 62.03, "skills_match": 71.66666666666666, "experience_score": 50.0, "education_score": 30.0, "keywords_match": 68.0, "overall_fit": 60.0, "growth_potential": 65.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks direct experience with MySQL queries and version control systems.", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology", "CGPA: 8.11"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.166489601135254, "processed_at": "2025-07-10T14:31:27.997271", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Dedicated and passionate coder with a strong desire to excel."}, {"id": "resume_21", "filename": "Profile_6.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 0, "keywords_match": 66.66666666666667, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in software development and AI, but limited experience with backend development.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Generative AI", "Data", "ML, AI"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Python fundamentals"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.31013798713684, "processed_at": "2025-07-10T14:31:28.006342", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_6.pdf", "file_size": 0, "word_count": 524, "success": true, "error": null}, "summary": "Senior Software Engineer with expertise in Generative AI, Data, ML, AI."}, {"id": "resume_22", "filename": "Profile_8.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 45.0, "education_score": 20.0, "keywords_match": 76.66666666666667, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has relevant certifications and awards, but lacks direct experience with Python data structures.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Version control systems like Git"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["IEEE UEMCON Conference", "WBUT Merit Certificate for Academic Excellence in Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Problem-solving skills", "Algorithmic thinking"], "weaknesses": ["Lack of experience with Python data structures"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.551103591918945, "processed_at": "2025-07-10T14:31:28.015263", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_8.pdf", "file_size": 0, "word_count": 435, "success": true, "error": null}, "summary": "Freshers with strong problem-solving skills and algorithmic thinking."}, {"id": "resume_23", "filename": "resume_sasidhar.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 89.15, "skills_match": 100, "experience_score": 85.0, "education_score": 40.0, "keywords_match": 93.33333333333333, "overall_fit": 83.0, "growth_potential": 85.0}, "recommendation": {"decision": "HIRE", "reason": "Relevant experience in backend development and database integration, but limited exposure to full-stack development.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Python", "MySQL", "Git"], "missing_skills": [], "skill_match_percentage": 100.0}, "experience_analysis": {"matching_experience": ["Intern ship"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong grasp of version control and collaborative development using Git"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": [], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 14.847094297409058, "processed_at": "2025-07-10T14:31:28.024262", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume_sasidhar.pdf", "file_size": 0, "word_count": 578, "success": true, "error": null}, "summary": "Detail-oriented developer with hands-on experience designing and deploying Python-based web applications using Django."}], "top_candidates": [{"id": "resume_23", "filename": "resume_sasidhar.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 89.15, "skills_match": 100, "experience_score": 85.0, "education_score": 40.0, "keywords_match": 93.33333333333333, "overall_fit": 83.0, "growth_potential": 85.0}, "recommendation": {"decision": "HIRE", "reason": "Relevant experience in backend development and database integration, but limited exposure to full-stack development.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Python", "MySQL", "Git"], "missing_skills": [], "skill_match_percentage": 100.0}, "experience_analysis": {"matching_experience": ["Intern ship"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong grasp of version control and collaborative development using Git"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": [], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 14.847094297409058, "processed_at": "2025-07-10T14:31:28.024262", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume_sasidhar.pdf", "file_size": 0, "word_count": 578, "success": true, "error": null}, "summary": "Detail-oriented developer with hands-on experience designing and deploying Python-based web applications using Django."}, {"id": "resume_20", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 62.03, "skills_match": 71.66666666666666, "experience_score": 50.0, "education_score": 30.0, "keywords_match": 68.0, "overall_fit": 60.0, "growth_potential": 65.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks direct experience with MySQL queries and version control systems.", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology", "CGPA: 8.11"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.166489601135254, "processed_at": "2025-07-10T14:31:27.997271", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Dedicated and passionate coder with a strong desire to excel."}, {"id": "resume_5", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 59.38, "skills_match": 71.66666666666666, "experience_score": 85.0, "education_score": 0, "keywords_match": 19.333333333333332, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON>k of required skills and experience for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Exposure to version control systems like Git"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.135844707489014, "processed_at": "2025-07-10T14:31:27.892263", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": ""}, {"id": "resume_19", "filename": "Profile_9.pdf", "candidate_name": "Top Skills", "scores": {"final_score": 26.67, "skills_match": 33.33333333333333, "experience_score": 85.0, "education_score": 0, "keywords_match": 58.0, "overall_fit": 78.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant experience and skills, but limited fit for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python"], "missing_skills": ["MySQL", "Git"], "skill_match_percentage": 33.3}, "experience_analysis": {"matching_experience": ["Ex- Microsoft", "Ex- Amazon"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Innovation", "Scalability"], "weaknesses": ["Lack of experience in backend development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Python fundamentals"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.852697372436523, "processed_at": "2025-07-10T14:31:27.990270", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_9.pdf", "file_size": 0, "word_count": 383, "success": true, "error": null}, "summary": "Software Engineer with experience in Android and distributed systems."}, {"id": "resume_1", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 5, "experience_score": 95.0, "education_score": 0, "keywords_match": 93.0, "overall_fit": 95.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in backend development and data-driven applications, but missing some required skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Backend development", "Data-driven applications", "Version control systems"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Problem-solving, Algorithmic thinking"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 6.258759498596191, "processed_at": "2025-07-10T14:31:27.842273", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Experienced software engineer with a strong background in backend development and problem-solving."}, {"id": "resume_2", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 60.0, "keywords_match": 45.333333333333336, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks specific MySQL skills and experience in backend development.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Software Development Engineer II", "Software Development Manager"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science", "Bachelor of Engineering (BE), Computer Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["MySQL queries", "Backend development"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.984562873840332, "processed_at": "2025-07-10T14:31:27.848265", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": "Software Development Manager with 6 years of experience in Amazon."}, {"id": "resume_3", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 25.0, "education_score": 0, "keywords_match": 1.3333333333333321, "overall_fit": 53.0, "growth_potential": 40.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.229778051376343, "processed_at": "2025-07-10T14:31:27.858271", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_4", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 95.0, "education_score": 0, "keywords_match": 66.66666666666667, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience and skills, but limited education match.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["17 years of development experience", "Commercial software development"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Data driven", "Lean startup practitioner"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Commercial software development", "Lean startup practices"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.124152421951294, "processed_at": "2025-07-10T14:31:27.875265", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of management experience and 17 years of development experience."}, {"id": "resume_6", "filename": "Profile_14.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 40.0, "education_score": 0, "keywords_match": 11.333333333333332, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant training expertise and project management skills, but lack of backend development experience.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["NLP practitioner", "Corporate training experience"], "experience_gaps": ["No backend development experience"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["M-Technology Business Development Manager"], "education_level": "BASIC"}, "assessment": {"strengths": ["Training expertise", "Project management skills"], "weaknesses": ["Lack of relevant work experience"], "red_flags": [], "cultural_fit_indicators": ["Friendly, collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of relevant work experience", "Backend development skills"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.406545877456665, "processed_at": "2025-07-10T14:31:27.898263", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_14.pdf", "file_size": 0, "word_count": 218, "success": true, "error": null}, "summary": "Freelance facilitator with 6+ years of corporate training experience and M-Technology Business Development Manager background."}, {"id": "resume_7", "filename": "Profile_15.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 85.0, "education_score": 90.0, "keywords_match": 41.33333333333333, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills, but limited experience in Python and MySQL.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Enterprise Integration Architecture", "SOA and Microservices", "Agile environment"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.E. (Electronics Telecommunications)"], "education_level": "ADVANCED"}, "assessment": {"strengths": ["Customer focused individual", "Strong understanding of Core Java, Web Services (SOAP/REST)"], "weaknesses": ["Lack of experience in backend development and data-driven applications"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.339215993881226, "processed_at": "2025-07-10T14:31:27.905263", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_15.pdf", "file_size": 0, "word_count": 318, "success": true, "error": null}, "summary": "Customer focused individual with strong understanding of Core Java and Web Services."}]}