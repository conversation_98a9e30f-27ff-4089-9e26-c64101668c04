{% extends 'ecom/admin_base.html' %}
{% load static %}

{% block content %}
<br><br><br><br><br>

<div class="container-fluid mt-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1 fw-bold">📊 Resume Analysis Report</h2>
                            <p class="mb-0 opacity-75">{{ filename }} - {{ job_type }}</p>
                        </div>
                        <div class="text-end">
                            <small class="opacity-75">Generated: {{ file_content.meta.exported_at|date:"M d, Y H:i" }}</small>
                            <br>
                            <small class="opacity-75">Version: {{ file_content.meta.version }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="display-4 text-primary mb-2">{{ file_content.meta.total_candidates }}</div>
                    <h6 class="text-muted mb-0">Total Candidates</h6>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="display-4 text-success mb-2">{{ file_content.meta.successful_analyses }}</div>
                    <h6 class="text-muted mb-0">Successful Analyses</h6>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="display-4 text-warning mb-2">{{ file_content.summary_statistics.average_score|floatformat:1 }}</div>
                    <h6 class="text-muted mb-0">Average Score</h6>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="display-4 text-info mb-2">{{ file_content.summary_statistics.processing_time|floatformat:1 }}s</div>
                    <h6 class="text-muted mb-0">Processing Time</h6>
                </div>
            </div>
        </div>
    </div>

    <!-- Score Distribution and Recommendations -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-light border-0">
                    <h5 class="mb-0 fw-bold">📈 Score Distribution</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 col-md-3 mb-3">
                            <div class="badge bg-success fs-6 mb-2">{{ file_content.summary_statistics.score_distribution.excellent }}</div>
                            <div class="small text-muted">Excellent</div>
                        </div>
                        <div class="col-6 col-md-3 mb-3">
                            <div class="badge bg-info fs-6 mb-2">{{ file_content.summary_statistics.score_distribution.good }}</div>
                            <div class="small text-muted">Good</div>
                        </div>
                        <div class="col-6 col-md-3 mb-3">
                            <div class="badge bg-warning fs-6 mb-2">{{ file_content.summary_statistics.score_distribution.average }}</div>
                            <div class="small text-muted">Average</div>
                        </div>
                        <div class="col-6 col-md-3 mb-3">
                            <div class="badge bg-danger fs-6 mb-2">{{ file_content.summary_statistics.score_distribution.below_average }}</div>
                            <div class="small text-muted">Below Avg</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-light border-0">
                    <h5 class="mb-0 fw-bold">💼 Hiring Recommendations</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4 mb-3">
                            <div class="badge bg-success fs-6 mb-2">{{ file_content.summary_statistics.recommendations.HIRE }}</div>
                            <div class="small text-muted">Hire</div>
                        </div>
                        <div class="col-4 mb-3">
                            <div class="badge bg-warning fs-6 mb-2">{{ file_content.summary_statistics.recommendations.CONSIDER }}</div>
                            <div class="small text-muted">Consider</div>
                        </div>
                        <div class="col-4 mb-3">
                            <div class="badge bg-danger fs-6 mb-2">{{ file_content.summary_statistics.recommendations.REJECT }}</div>
                            <div class="small text-muted">Reject</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Job Description -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light border-0">
                    <h5 class="mb-0 fw-bold">📋 Job Description</h5>
                </div>
                <div class="card-body">
                    <div class="bg-light p-3 rounded">
                        <pre class="mb-0 text-wrap" style="white-space: pre-wrap;">{{ file_content.meta.job_description }}</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Candidates Section -->
    {% if file_content.top_candidates %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light border-0">
                    <h5 class="mb-0 fw-bold">🏆 Top Candidates</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for candidate in file_content.top_candidates %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-start border-primary border-3 h-100">
                                <div class="card-body">
                                    <h6 class="fw-bold text-primary mb-2">{{ candidate.candidate_name }}</h6>
                                    <p class="small text-muted mb-2">{{ candidate.filename }}</p>

                                    <div class="mb-2">
                                        <span class="badge bg-primary">Score: {{ candidate.scores.final_score|floatformat:1 }}</span>
                                        {% if candidate.recommendation.decision == "HIRE" %}
                                            <span class="badge bg-success">{{ candidate.recommendation.decision }}</span>
                                        {% elif candidate.recommendation.decision == "CONSIDER" %}
                                            <span class="badge bg-warning">{{ candidate.recommendation.decision }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ candidate.recommendation.decision }}</span>
                                        {% endif %}
                                    </div>

                                    <div class="small">
                                        <div class="mb-1">
                                            <strong>Skills Match:</strong> {{ candidate.skills_analysis.skill_match_percentage|floatformat:1 }}%
                                        </div>
                                        <div class="mb-1">
                                            <strong>Experience:</strong> {{ candidate.experience_analysis.experience_level }}
                                        </div>
                                        {% if candidate.summary %}
                                        <div class="text-muted">
                                            <em>{{ candidate.summary|truncatechars:100 }}</em>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Candidates Table -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 fw-bold">👥 All Candidates</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" onclick="exportToCSV()">
                            <i class="bi bi-download"></i> Export CSV
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleFilters()">
                            <i class="bi bi-funnel"></i> Filters
                        </button>
                    </div>
                </div>

                <!-- Filters Panel -->
                <div id="filtersPanel" class="card-body border-bottom bg-light" style="display: none;">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label small">Recommendation</label>
                            <select class="form-select form-select-sm" id="recommendationFilter">
                                <option value="">All</option>
                                <option value="HIRE">Hire</option>
                                <option value="CONSIDER">Consider</option>
                                <option value="REJECT">Reject</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label small">Experience Level</label>
                            <select class="form-select form-select-sm" id="experienceFilter">
                                <option value="">All</option>
                                <option value="JUNIOR">Junior</option>
                                <option value="MID">Mid</option>
                                <option value="SENIOR">Senior</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label small">Min Score</label>
                            <input type="number" class="form-control form-control-sm" id="minScoreFilter" placeholder="0" min="0" max="100">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label small">Search Name</label>
                            <input type="text" class="form-control form-control-sm" id="nameFilter" placeholder="Search candidate...">
                        </div>
                    </div>
                </div>

                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="candidatesTable">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0">Candidate</th>
                                    <th class="border-0">Score</th>
                                    <th class="border-0">Skills Match</th>
                                    <th class="border-0">Experience</th>
                                    <th class="border-0">Recommendation</th>
                                    <th class="border-0">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for candidate in file_content.candidates %}
                                <tr class="candidate-row"
                                    data-recommendation="{{ candidate.recommendation.decision }}"
                                    data-experience="{{ candidate.experience_analysis.experience_level }}"
                                    data-score="{{ candidate.scores.final_score }}"
                                    data-name="{{ candidate.candidate_name|lower }}">
                                    <td>
                                        <div>
                                            <strong>{{ candidate.candidate_name }}</strong>
                                            <br>
                                            <small class="text-muted">{{ candidate.filename }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="progress me-2" style="width: 60px; height: 8px;">
                                                <div class="progress-bar
                                                    {% if candidate.scores.final_score >= 80 %}bg-success
                                                    {% elif candidate.scores.final_score >= 60 %}bg-info
                                                    {% elif candidate.scores.final_score >= 40 %}bg-warning
                                                    {% else %}bg-danger{% endif %}"
                                                    style="width: {{ candidate.scores.final_score }}%"></div>
                                            </div>
                                            <span class="fw-bold">{{ candidate.scores.final_score|floatformat:1 }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge
                                            {% if candidate.skills_analysis.skill_match_percentage >= 70 %}bg-success
                                            {% elif candidate.skills_analysis.skill_match_percentage >= 50 %}bg-info
                                            {% elif candidate.skills_analysis.skill_match_percentage >= 30 %}bg-warning
                                            {% else %}bg-danger{% endif %}">
                                            {{ candidate.skills_analysis.skill_match_percentage|floatformat:1 }}%
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge
                                            {% if candidate.experience_analysis.experience_level == "SENIOR" %}bg-success
                                            {% elif candidate.experience_analysis.experience_level == "MID" %}bg-info
                                            {% else %}bg-warning{% endif %}">
                                            {{ candidate.experience_analysis.experience_level }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if candidate.recommendation.decision == "HIRE" %}
                                            <span class="badge bg-success">{{ candidate.recommendation.decision }}</span>
                                        {% elif candidate.recommendation.decision == "CONSIDER" %}
                                            <span class="badge bg-warning">{{ candidate.recommendation.decision }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ candidate.recommendation.decision }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="showCandidateDetails('{{ candidate.id }}')">
                                            <i class="bi bi-eye"></i> Details
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Candidate Details Modal -->
<div class="modal fade" id="candidateModal" tabindex="-1" aria-labelledby="candidateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="candidateModalLabel">Candidate Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="candidateModalBody">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

<script>
// Store candidate data for JavaScript access
const candidatesData = {{ file_content.candidates|safe }};

// Toggle filters panel
function toggleFilters() {
    const panel = document.getElementById('filtersPanel');
    panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
}

// Filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const recommendationFilter = document.getElementById('recommendationFilter');
    const experienceFilter = document.getElementById('experienceFilter');
    const minScoreFilter = document.getElementById('minScoreFilter');
    const nameFilter = document.getElementById('nameFilter');

    [recommendationFilter, experienceFilter, minScoreFilter, nameFilter].forEach(filter => {
        filter.addEventListener('change', applyFilters);
        filter.addEventListener('input', applyFilters);
    });
});

function applyFilters() {
    const recommendationValue = document.getElementById('recommendationFilter').value;
    const experienceValue = document.getElementById('experienceFilter').value;
    const minScoreValue = parseFloat(document.getElementById('minScoreFilter').value) || 0;
    const nameValue = document.getElementById('nameFilter').value.toLowerCase();

    const rows = document.querySelectorAll('.candidate-row');

    rows.forEach(row => {
        const recommendation = row.dataset.recommendation;
        const experience = row.dataset.experience;
        const score = parseFloat(row.dataset.score);
        const name = row.dataset.name;

        let show = true;

        if (recommendationValue && recommendation !== recommendationValue) show = false;
        if (experienceValue && experience !== experienceValue) show = false;
        if (score < minScoreValue) show = false;
        if (nameValue && !name.includes(nameValue)) show = false;

        row.style.display = show ? '' : 'none';
    });
}

// Show candidate details in modal
function showCandidateDetails(candidateId) {
    const candidate = candidatesData.find(c => c.id === candidateId);
    if (!candidate) return;

    const modalBody = document.getElementById('candidateModalBody');
    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <div class="card border-0 bg-light mb-3">
                    <div class="card-body">
                        <h6 class="fw-bold mb-3">📊 Scores Breakdown</h6>
                        <div class="row">
                            <div class="col-6 mb-2">
                                <small class="text-muted">Final Score</small>
                                <div class="fw-bold">${candidate.scores.final_score.toFixed(1)}</div>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted">Skills Match</small>
                                <div class="fw-bold">${candidate.scores.skills_match.toFixed(1)}</div>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted">Experience</small>
                                <div class="fw-bold">${candidate.scores.experience_score.toFixed(1)}</div>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted">Education</small>
                                <div class="fw-bold">${candidate.scores.education_score.toFixed(1)}</div>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted">Keywords</small>
                                <div class="fw-bold">${candidate.scores.keywords_match.toFixed(1)}</div>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted">Growth Potential</small>
                                <div class="fw-bold">${candidate.scores.growth_potential.toFixed(1)}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card border-0 bg-light mb-3">
                    <div class="card-body">
                        <h6 class="fw-bold mb-3">💼 Recommendation</h6>
                        <div class="mb-2">
                            <span class="badge ${candidate.recommendation.decision === 'HIRE' ? 'bg-success' :
                                candidate.recommendation.decision === 'CONSIDER' ? 'bg-warning' : 'bg-danger'} fs-6">
                                ${candidate.recommendation.decision}
                            </span>
                            <span class="badge bg-secondary ms-2">${candidate.recommendation.confidence}</span>
                        </div>
                        <p class="small mb-0">${candidate.recommendation.reason}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="card border-0 bg-light mb-3">
                    <div class="card-body">
                        <h6 class="fw-bold mb-3">🛠️ Skills Analysis</h6>
                        <div class="mb-2">
                            <small class="text-muted">Match Percentage</small>
                            <div class="fw-bold text-primary">${candidate.skills_analysis.skill_match_percentage.toFixed(1)}%</div>
                        </div>
                        ${candidate.skills_analysis.matching_skills.length > 0 ? `
                            <div class="mb-2">
                                <small class="text-muted">Matching Skills</small>
                                <div>${candidate.skills_analysis.matching_skills.map(skill =>
                                    `<span class="badge bg-success me-1 mb-1">${skill}</span>`
                                ).join('')}</div>
                            </div>
                        ` : ''}
                        ${candidate.skills_analysis.missing_skills.length > 0 ? `
                            <div>
                                <small class="text-muted">Missing Skills</small>
                                <div>${candidate.skills_analysis.missing_skills.map(skill =>
                                    `<span class="badge bg-danger me-1 mb-1">${skill}</span>`
                                ).join('')}</div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card border-0 bg-light mb-3">
                    <div class="card-body">
                        <h6 class="fw-bold mb-3">💼 Experience Analysis</h6>
                        <div class="mb-2">
                            <small class="text-muted">Level</small>
                            <div class="fw-bold">${candidate.experience_analysis.experience_level}</div>
                        </div>
                        ${candidate.experience_analysis.matching_experience.length > 0 ? `
                            <div class="mb-2">
                                <small class="text-muted">Matching Experience</small>
                                <ul class="small mb-0">
                                    ${candidate.experience_analysis.matching_experience.map(exp =>
                                        `<li>${exp}</li>`
                                    ).join('')}
                                </ul>
                            </div>
                        ` : ''}
                        ${candidate.experience_analysis.experience_gaps.length > 0 ? `
                            <div>
                                <small class="text-muted">Experience Gaps</small>
                                <ul class="small mb-0">
                                    ${candidate.experience_analysis.experience_gaps.map(gap =>
                                        `<li class="text-danger">${gap}</li>`
                                    ).join('')}
                                </ul>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card border-0 bg-light mb-3">
                    <div class="card-body">
                        <h6 class="fw-bold mb-3">🎓 Education Analysis</h6>
                        <div class="mb-2">
                            <small class="text-muted">Level</small>
                            <div class="fw-bold">${candidate.education_analysis.education_level}</div>
                        </div>
                        ${candidate.education_analysis.education_highlights.length > 0 ? `
                            <div>
                                <small class="text-muted">Highlights</small>
                                <ul class="small mb-0">
                                    ${candidate.education_analysis.education_highlights.map(highlight =>
                                        `<li>${highlight}</li>`
                                    ).join('')}
                                </ul>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        </div>

        ${candidate.assessment.strengths.length > 0 || candidate.assessment.weaknesses.length > 0 ? `
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 bg-light mb-3">
                        <div class="card-body">
                            <h6 class="fw-bold mb-3">📋 Assessment</h6>
                            <div class="row">
                                ${candidate.assessment.strengths.length > 0 ? `
                                    <div class="col-md-6">
                                        <small class="text-muted">Strengths</small>
                                        <ul class="small text-success">
                                            ${candidate.assessment.strengths.map(strength =>
                                                `<li>${strength}</li>`
                                            ).join('')}
                                        </ul>
                                    </div>
                                ` : ''}
                                ${candidate.assessment.weaknesses.length > 0 ? `
                                    <div class="col-md-6">
                                        <small class="text-muted">Weaknesses</small>
                                        <ul class="small text-warning">
                                            ${candidate.assessment.weaknesses.map(weakness =>
                                                `<li>${weakness}</li>`
                                            ).join('')}
                                        </ul>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        ` : ''}

        <div class="row">
            <div class="col-md-6">
                <div class="card border-0 bg-light mb-3">
                    <div class="card-body">
                        <h6 class="fw-bold mb-3">🎯 Hiring Insights</h6>
                        <div class="mb-2">
                            <small class="text-muted">Salary Alignment</small>
                            <div class="fw-bold">${candidate.hiring_insights.salary_expectation_alignment}</div>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">Onboarding Priority</small>
                            <div class="fw-bold">${candidate.hiring_insights.onboarding_priority}</div>
                        </div>
                        ${candidate.hiring_insights.interview_focus_areas.length > 0 ? `
                            <div>
                                <small class="text-muted">Interview Focus Areas</small>
                                <div>${candidate.hiring_insights.interview_focus_areas.map(area =>
                                    `<span class="badge bg-info me-1 mb-1">${area}</span>`
                                ).join('')}</div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card border-0 bg-light mb-3">
                    <div class="card-body">
                        <h6 class="fw-bold mb-3">📄 File Information</h6>
                        <div class="small">
                            <div class="mb-1"><strong>Filename:</strong> ${candidate.filename}</div>
                            <div class="mb-1"><strong>Word Count:</strong> ${candidate.metadata.word_count}</div>
                            <div class="mb-1"><strong>Processing Time:</strong> ${candidate.metadata.processing_time.toFixed(2)}s</div>
                            <div class="mb-1"><strong>Processed At:</strong> ${new Date(candidate.metadata.processed_at).toLocaleString()}</div>
                        </div>
                        ${candidate.summary ? `
                            <div class="mt-2">
                                <small class="text-muted">Summary</small>
                                <p class="small mb-0">${candidate.summary}</p>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('candidateModalLabel').textContent = `${candidate.candidate_name} - Detailed Analysis`;
    $('#candidateModal').modal('show');
}

// Export to CSV functionality
function exportToCSV() {
    const headers = ['Name', 'Filename', 'Final Score', 'Skills Match %', 'Experience Level', 'Recommendation', 'Reason'];
    const csvContent = [
        headers.join(','),
        ...candidatesData.map(candidate => [
            `"${candidate.candidate_name}"`,
            `"${candidate.filename}"`,
            candidate.scores.final_score.toFixed(1),
            candidate.skills_analysis.skill_match_percentage.toFixed(1),
            candidate.experience_analysis.experience_level,
            candidate.recommendation.decision,
            `"${candidate.recommendation.reason}"`
        ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'resume_analysis_report.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}
</script>

<!-- Include jQuery and Bootstrap JS if not already included -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>

{% endblock content %}