{"meta": {"version": "1.0", "exported_at": "2025-07-12T13:59:08.952089", "total_candidates": 23, "successful_analyses": 23, "failed_analyses": 0, "job_description": "\nSoftware Developer Position - TechCorp Inc.\n\nWe are seeking a skilled Software Developer to join our dynamic team.\n\nSKILLS:\n- 3+ years of experience in software development\n- Proficiency in Python, JavaScript, and SQL\n- Experience with web frameworks (Django, React, Node.js)\n- Knowledge of database systems (PostgreSQL, MongoDB)\n- Familiarity with cloud platforms (AWS, Azure)\n- Experience with version control (Git)\n- Understanding of Agile/Scrum methodologies\n\nQUALIFICATIONS:\n- Bachelor's degree in Computer Science or related field\n- Experience with containerization (Docker, Kubernetes)\n- Knowledge of CI/CD pipelines\n- Experience with microservices architecture\n- Strong problem-solving and communication skills\n\nRESPONSIBILITIES:\n- Develop and maintain web applications\n- Collaborate with cross-functional teams\n- Write clean, maintainable code\n- Participate in code reviews\n- Troubleshoot and debug applications\n- Stay updated with latest technologies\n\nBENEFITS:\n- Competitive salary\n- Health insurance\n- Remote work options\n- Professional development opportunities\n", "processed_at": "2025-07-12T13:59:08.920221"}, "summary_statistics": {"average_score": 1.04, "score_distribution": {"excellent": 0, "good": 0, "average": 0, "below_average": 23}, "recommendations": {"HIRE": 0, "CONSIDER": 0, "REJECT": 23}, "processing_time": 85.91}, "candidates": [{"id": "resume_1", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 60.0, "keywords_match": 78.2, "overall_fit": 85.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant experience, but missing some required skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["6 years 5 months Software Development Manager", "2 years 2 months Software Development Engineer II", "2 years 2 months Software Development Engineer"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science", "Bachelor of Engineering (BE), Computer Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong problem-solving and communication skills"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Missing containerization and CI/CD pipelines skills"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 8.0095534324646, "processed_at": "2025-07-12T13:59:08.682518", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": "Software Development Manager with 6+ years of experience"}, {"id": "resume_2", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 5, "education_score": 0, "keywords_match": 0, "overall_fit": 3, "growth_potential": 0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["3+ years of experience in software development"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.05613899230957, "processed_at": "2025-07-12T13:59:08.694381", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_3", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 17.5, "experience_score": 100, "education_score": 0, "keywords_match": 98.0, "overall_fit": 88.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant experience and skills, but some gaps in qualifications.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["<PERSON>er", "CI/CD"], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "Kubernetes", "Git", "Microservices", "Agile", "Scrum"], "skill_match_percentage": 12.5}, "experience_analysis": {"matching_experience": ["Develop and maintain web applications", "Collaborate with cross-functional teams", "Write clean, maintainable code", "Participate in code reviews", "Troubleshoot and debug applications", "Stay updated with latest technologies"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong problem-solving skills", "Excellent communication skills"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills", "Problem-solving skills"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.205002307891846, "processed_at": "2025-07-12T13:59:08.705677", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Experienced software engineer with strong problem-solving skills and excellent communication skills."}, {"id": "resume_4", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 18.75, "experience_score": 95.0, "education_score": 70.0, "keywords_match": 51.2, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience and skills, but limited experience in specific areas.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["AWS", "Agile", "Scrum"], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "CI/CD"], "skill_match_percentage": 18.8}, "experience_analysis": {"matching_experience": ["17 years of development experience", "Commercial software development", "Lean startup practitioner"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Computer Science or related field"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Data driven and lean startup practitioner", "Excellent customer satisfaction"], "weaknesses": ["Limited experience with containerization"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Specific areas to focus on during interview"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 21.32561421394348, "processed_at": "2025-07-12T13:59:08.724361", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of management experience and 17 years of development experience."}, {"id": "resume_5", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 0, "skills_match": 23.75, "experience_score": 85.0, "education_score": 40.0, "keywords_match": 90.4, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant experience and skills, but some gaps in education and technical skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "React", "Microservices"], "missing_skills": ["JavaScript", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 18.8}, "experience_analysis": {"matching_experience": ["SDE Intern @ Goldman Sachs", "Member Technical at D.E. Shaw Co."], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Problem Solving (Intermediate)", "Problem Solving (Basic)"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Head Girl (2016-17)"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Problem-solving"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 8.583043098449707, "processed_at": "2025-07-12T13:59:08.749938", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": "Summary of candidate's profile"}, {"id": "resume_6", "filename": "Profile_15.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 18.75, "experience_score": 95.0, "education_score": 60.0, "keywords_match": 84.6, "overall_fit": 85.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in software development, but limited experience with modern technologies.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Microservices", "Agile", "Scrum"], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "CI/CD"], "skill_match_percentage": 18.8}, "experience_analysis": {"matching_experience": ["Web development", "Agile environment", "Team management"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Electronics and Telecommunications"], "education_level": "BASIC"}, "assessment": {"strengths": ["Customer focused individual", "Strong understanding of Core Java"], "weaknesses": ["Limited experience with cloud platforms"], "red_flags": [], "cultural_fit_indicators": ["Remote work options"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Modern technologies", "Team management"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.025634765625, "processed_at": "2025-07-12T13:59:08.763296", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_15.pdf", "file_size": 0, "word_count": 318, "success": true, "error": null}, "summary": "Customer focused individual with 12.5 years of professional experience in Software Design Development."}, {"id": "resume_7", "filename": "Profile_16.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 70.0, "keywords_match": 43.2, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Containerization (Docker, Kubernetes)", "CI/CD pipelines", "Microservices architecture"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Computer Science or related field"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Strong problem-solving and communication skills"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical questions", "Problem-solving skills"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 19.338947057724, "processed_at": "2025-07-12T13:59:08.770336", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_16.pdf", "file_size": 0, "word_count": 120, "success": true, "error": null}, "summary": "Senior software engineer with experience in distributed systems and software engineering"}, {"id": "resume_8", "filename": "Profile_17.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 6.25, "experience_score": 90.0, "education_score": 20.0, "keywords_match": 89.8, "overall_fit": 88.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has relevant experience, but lacks some required skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["AWS"], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 6.2}, "experience_analysis": {"matching_experience": ["9 years 1 month Software Engineering Manager, AWS Bedrock March 2024 - Present", "2 years 3 months Senior Software Development Engineer November 2021 - December 2022"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Computer Science or related field"], "education_level": "BASIC"}, "assessment": {"strengths": ["Experience with cloud platforms (AWS, Azure)", "Familiarity with version control (Git)"], "weaknesses": ["Lack of experience with JavaScript", "No experience with CI/CD pipelines"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of experience with JavaScript", "No experience with CI/CD pipelines"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 17.715458869934082, "processed_at": "2025-07-12T13:59:08.775922", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_17.pdf", "file_size": 0, "word_count": 150, "success": true, "error": null}, "summary": "Software Engineering Manager with 9 years of experience in AWS and cloud platforms."}, {"id": "resume_9", "filename": "Profile_14.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 30.0, "education_score": 0, "keywords_match": 14.0, "overall_fit": 38.0, "growth_potential": 25.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills and experience", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Develop and maintain web applications", "Collaborate with cross-functional teams", "Write clean, maintainable code", "Participate in code reviews", "Troubleshoot and debug applications", "Stay updated with latest technologies"], "experience_gaps": ["Experience with containerization (<PERSON><PERSON>, Kubernetes)", "Knowledge of CI/CD pipelines", "Experience with microservices architecture"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 19.397598028182983, "processed_at": "2025-07-12T13:59:08.783018", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_14.pdf", "file_size": 0, "word_count": 218, "success": true, "error": null}, "summary": ""}, {"id": "resume_10", "filename": "Profile_18.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate lacks relevant skills and experience for the position.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["3+ years of experience in software development"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor of Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Lack of relevant work experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 17.115657329559326, "processed_at": "2025-07-12T13:59:08.788709", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_18.pdf", "file_size": 0, "word_count": 118, "success": true, "error": null}, "summary": ""}, {"id": "resume_11", "filename": "Profile_19.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 18.75, "experience_score": 95.0, "education_score": 70.0, "keywords_match": 49.6, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant experience and skills, but limited proficiency in some areas.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["<PERSON>er", "Kubernetes", "Agile"], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "Git", "Microservices", "Scrum", "CI/CD"], "skill_match_percentage": 18.8}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["3+ years of experience in software development"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "STANDARD"}, "assessment": {"strengths": ["Building distributed, cloud native and server-side applications", "Leading agile development teams"], "weaknesses": ["Limited working knowledge of Spanish"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical leadership", "Cloud native applications"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 20.007872581481934, "processed_at": "2025-07-12T13:59:08.801955", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_19.pdf", "file_size": 0, "word_count": 546, "success": true, "error": null}, "summary": "Programmer, architect and technical lead with expertise in C++, Golang, Rust, Linux, Envoy, HTTP, TLS, multi-threaded distributed network services, container engines Docker, Kubernetes."}, {"id": "resume_12", "filename": "Profile_2.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 6.25, "experience_score": 80.0, "education_score": 40.0, "keywords_match": 88.0, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has relevant experience, but some skills are missing.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["React"], "missing_skills": ["Python", "JavaScript", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 6.2}, "experience_analysis": {"matching_experience": ["6 months", "4 months", "6 months", "6 months"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Bachelor of Technology - BTech, Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Problem-solving skills", "Communication skills"], "weaknesses": ["Limited experience in containerization and CI/CD pipelines"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Containerization", "CI/CD pipelines"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 17.213226079940796, "processed_at": "2025-07-12T13:59:08.806625", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_2.pdf", "file_size": 0, "word_count": 119, "success": true, "error": null}, "summary": "Software Developer with 6+ years of experience in software development and relevant certifications."}, {"id": "resume_13", "filename": "Profile_4.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 6.25, "experience_score": 55.0, "education_score": 0, "keywords_match": 27.2, "overall_fit": 58.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks relevant technical skills and experience for the position.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["JavaScript"], "missing_skills": ["Python", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 6.2}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["3+ years of experience in software development"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 17.16521644592285, "processed_at": "2025-07-12T13:59:08.817870", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_4.pdf", "file_size": 0, "word_count": 419, "success": true, "error": null}, "summary": ""}, {"id": "resume_14", "filename": "Profile_21.pdf", "candidate_name": "Ka<PERSON>ya E.", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 20.0, "education_score": 0, "keywords_match": 0, "overall_fit": 33.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate lacks required technical skills for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["3+ years of experience in software development"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Computer Science or related field"], "education_level": "BASIC"}, "assessment": {"strengths": ["Experience in implementing recruiting strategies,sourcing,attracting talent and driving business goals"], "weaknesses": ["Lack of relevant technical skills for Software Developer position"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of relevant technical skills", "Experience in software development"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.4051296710968, "processed_at": "2025-07-12T13:59:08.823467", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_21.pdf", "file_size": 0, "word_count": 169, "success": true, "error": null}, "summary": "HR Talent Acquisition professional with experience in recruiting and talent acquisition"}, {"id": "resume_15", "filename": "Profile_20.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 20.0, "keywords_match": 61.6, "overall_fit": 68.0, "growth_potential": 50.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the position.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Experience Tata Consultancy Services HR- Talent Acquisition Group Sourcing June 2024 - Present"], "experience_gaps": ["3+ years of experience in software development"], "experience_level": "MID"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 17.744316577911377, "processed_at": "2025-07-12T13:59:08.830995", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_20.pdf", "file_size": 0, "word_count": 232, "success": true, "error": null}, "summary": "HR professional with 2.5 years of experience in IT and non IT industry."}, {"id": "resume_16", "filename": "Profile_3.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 0, "education_score": 0, "keywords_match": 0, "overall_fit": 3, "growth_potential": 0}, "recommendation": {"decision": "REJECT", "reason": "Candidate lacks relevant work experience and programming skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["3+ years of experience in software development", "Proficiency in Python, JavaScript, and SQL", "Experience with web frameworks (Django, React, Node.js)", "Knowledge of database systems (PostgreSQL, MongoDB)", "Familiarity with cloud platforms (AWS, Azure)", "Experience with version control (Git)", "Understanding of Agile/Scrum methodologies"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Lack of relevant work experience", "No programming skills mentioned"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.730857610702515, "processed_at": "2025-07-12T13:59:08.838809", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_3.pdf", "file_size": 0, "word_count": 289, "success": true, "error": null}, "summary": ""}, {"id": "resume_17", "filename": "Profile_7.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 95.0, "education_score": 80.0, "keywords_match": 89.8, "overall_fit": 95.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks required experience in containerization and CI/CD pipelines.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Experience with web frameworks (Django, React, Node.js)"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Computer Science or related field"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Strong problem-solving and communication skills"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Problem-solving abilities"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 17.57828426361084, "processed_at": "2025-07-12T13:59:08.852857", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_7.pdf", "file_size": 0, "word_count": 602, "success": true, "error": null}, "summary": "Passionate competitive programmer with strong foundation in data structures and algorithms."}, {"id": "resume_18", "filename": "Profile_5.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 60.0, "keywords_match": 80.6, "overall_fit": 85.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON><PERSON> required experience in software development and containerization.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["3+ years of experience in software development"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Computer Science or related field"], "education_level": "BASIC"}, "assessment": {"strengths": ["Robust problem-solving and the ability to manage high-volume data workloads", "Leadership and mentorship"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills", "Leadership and mentorship"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 18.355425357818604, "processed_at": "2025-07-12T13:59:08.860984", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_5.pdf", "file_size": 0, "word_count": 290, "success": true, "error": null}, "summary": "Transformative journey as a Member of Technical Staff at Oracle with expertise in Apache Kafka and Kafka Streams."}, {"id": "resume_19", "filename": "Profile_6.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 6.25, "experience_score": 95.0, "education_score": 0, "keywords_match": 97.2, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills and experience, but limited education match.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["JavaScript"], "missing_skills": ["Python", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 6.2}, "experience_analysis": {"matching_experience": ["Develop and maintain web applications", "Collaborate with cross-functional teams", "Write clean, maintainable code", "Troubleshoot and debug applications"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Computer Science or related field"], "education_level": "BASIC"}, "assessment": {"strengths": ["Technical Advocacy", "Generative AI", "Software Solutions"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Remote work options"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical questions", "AI and data science applications"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 18.020196199417114, "processed_at": "2025-07-12T13:59:08.873677", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_6.pdf", "file_size": 0, "word_count": 524, "success": true, "error": null}, "summary": "Senior Software Engineer with expertise in software development, AI, and data science."}, {"id": "resume_20", "filename": "Profile_9.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 12.5, "experience_score": 95.0, "education_score": 80.0, "keywords_match": 59.6, "overall_fit": 91.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills, but limited experience in specific areas.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "Azure"], "missing_skills": ["JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 12.5}, "experience_analysis": {"matching_experience": ["Developed and maintained software applications", "Collaborated with cross-functional teams", "Troubleshoot and debug applications"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Computer Science or related field"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Innovation", "Scalability", "Problem-solving skills"], "weaknesses": ["Limited experience with CI/CD pipelines"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Problem-solving abilities"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 18.69372034072876, "processed_at": "2025-07-12T13:59:08.882015", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_9.pdf", "file_size": 0, "word_count": 383, "success": true, "error": null}, "summary": "Senior Software Engineer with expertise in Android and distributed systems."}, {"id": "resume_21", "filename": "Profile_8.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 12.5, "experience_score": 55.0, "education_score": 20.0, "keywords_match": 28.8, "overall_fit": 58.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has some matching skills and strengths, but lacks relevant technical experience.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["React", "AWS"], "missing_skills": ["Python", "JavaScript", "Node.js", "Django", "PostgreSQL", "MongoDB", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 12.5}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["3+ years of experience in software development"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Computer Science or related field", "Merit Certificate for Academic Excellency in Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong problem-solving and communication skills"], "weaknesses": ["Lack of relevant technical experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of relevant technical experience", "Familiarity with cloud platforms (AWS, Azure)"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 18.22798991203308, "processed_at": "2025-07-12T13:59:08.894295", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_8.pdf", "file_size": 0, "word_count": 435, "success": true, "error": null}, "summary": "<PERSON><PERSON><PERSON> is a computer science graduate with strong problem-solving and communication skills."}, {"id": "resume_22", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 0, "skills_match": 11.25, "experience_score": 40.0, "education_score": 20.0, "keywords_match": 45.6, "overall_fit": 60.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Not enough relevant experience for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python"], "missing_skills": ["JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 6.2}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["3+ years of experience in software development"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology", "CGPA: 8.11"], "education_level": "BASIC"}, "assessment": {"strengths": ["Dedicated and passionate coder", "Technologically adept"], "weaknesses": ["Lack of relevant work experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of relevant work experience", "Technical skills"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 19.505969524383545, "processed_at": "2025-07-12T13:59:08.902977", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Dedicated and passionate coder with a strong desire to excel"}, {"id": "resume_23", "filename": "resume_sasidhar.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 90.0, "education_score": 70.0, "keywords_match": 97.8, "overall_fit": 90.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has relevant experience and skills but lacks some required qualifications.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "JavaScript", "Django", "Git"], "missing_skills": ["React", "Node.js", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": ["Intern ship , SwechaAP Contributed as a front-end and back-end developer during an 8-week internship focused on building educational web applications using DevOps methodologies"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "STANDARD"}, "assessment": {"strengths": ["Strong grasp of version control and collaborative development", "Known for delivering robust solutions that balance performance, usability, and maintainability"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills", "Problem-solving abilities"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 18.0801043510437, "processed_at": "2025-07-12T13:59:08.918837", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\resume_sasidhar.pdf", "file_size": 0, "word_count": 578, "success": true, "error": null}, "summary": "Detail-oriented developer with hands-on experience designing and deploying Python-based web applications using Django."}], "top_candidates": [{"id": "resume_23", "filename": "resume_sasidhar.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 90.0, "education_score": 70.0, "keywords_match": 97.8, "overall_fit": 90.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has relevant experience and skills but lacks some required qualifications.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "JavaScript", "Django", "Git"], "missing_skills": ["React", "Node.js", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": ["Intern ship , SwechaAP Contributed as a front-end and back-end developer during an 8-week internship focused on building educational web applications using DevOps methodologies"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "STANDARD"}, "assessment": {"strengths": ["Strong grasp of version control and collaborative development", "Known for delivering robust solutions that balance performance, usability, and maintainability"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills", "Problem-solving abilities"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 18.0801043510437, "processed_at": "2025-07-12T13:59:08.918837", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\resume_sasidhar.pdf", "file_size": 0, "word_count": 578, "success": true, "error": null}, "summary": "Detail-oriented developer with hands-on experience designing and deploying Python-based web applications using Django."}, {"id": "resume_1", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 60.0, "keywords_match": 78.2, "overall_fit": 85.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant experience, but missing some required skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["6 years 5 months Software Development Manager", "2 years 2 months Software Development Engineer II", "2 years 2 months Software Development Engineer"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science", "Bachelor of Engineering (BE), Computer Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong problem-solving and communication skills"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Missing containerization and CI/CD pipelines skills"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 8.0095534324646, "processed_at": "2025-07-12T13:59:08.682518", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": "Software Development Manager with 6+ years of experience"}, {"id": "resume_2", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 5, "education_score": 0, "keywords_match": 0, "overall_fit": 3, "growth_potential": 0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["3+ years of experience in software development"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.05613899230957, "processed_at": "2025-07-12T13:59:08.694381", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_3", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 17.5, "experience_score": 100, "education_score": 0, "keywords_match": 98.0, "overall_fit": 88.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant experience and skills, but some gaps in qualifications.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["<PERSON>er", "CI/CD"], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "Kubernetes", "Git", "Microservices", "Agile", "Scrum"], "skill_match_percentage": 12.5}, "experience_analysis": {"matching_experience": ["Develop and maintain web applications", "Collaborate with cross-functional teams", "Write clean, maintainable code", "Participate in code reviews", "Troubleshoot and debug applications", "Stay updated with latest technologies"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong problem-solving skills", "Excellent communication skills"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills", "Problem-solving skills"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.205002307891846, "processed_at": "2025-07-12T13:59:08.705677", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Experienced software engineer with strong problem-solving skills and excellent communication skills."}, {"id": "resume_4", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 18.75, "experience_score": 95.0, "education_score": 70.0, "keywords_match": 51.2, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience and skills, but limited experience in specific areas.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["AWS", "Agile", "Scrum"], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "CI/CD"], "skill_match_percentage": 18.8}, "experience_analysis": {"matching_experience": ["17 years of development experience", "Commercial software development", "Lean startup practitioner"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Computer Science or related field"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Data driven and lean startup practitioner", "Excellent customer satisfaction"], "weaknesses": ["Limited experience with containerization"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Specific areas to focus on during interview"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 21.32561421394348, "processed_at": "2025-07-12T13:59:08.724361", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of management experience and 17 years of development experience."}, {"id": "resume_5", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 0, "skills_match": 23.75, "experience_score": 85.0, "education_score": 40.0, "keywords_match": 90.4, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant experience and skills, but some gaps in education and technical skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "React", "Microservices"], "missing_skills": ["JavaScript", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 18.8}, "experience_analysis": {"matching_experience": ["SDE Intern @ Goldman Sachs", "Member Technical at D.E. Shaw Co."], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Problem Solving (Intermediate)", "Problem Solving (Basic)"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Head Girl (2016-17)"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Problem-solving"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 8.583043098449707, "processed_at": "2025-07-12T13:59:08.749938", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": "Summary of candidate's profile"}, {"id": "resume_6", "filename": "Profile_15.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 18.75, "experience_score": 95.0, "education_score": 60.0, "keywords_match": 84.6, "overall_fit": 85.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in software development, but limited experience with modern technologies.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Microservices", "Agile", "Scrum"], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "CI/CD"], "skill_match_percentage": 18.8}, "experience_analysis": {"matching_experience": ["Web development", "Agile environment", "Team management"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Electronics and Telecommunications"], "education_level": "BASIC"}, "assessment": {"strengths": ["Customer focused individual", "Strong understanding of Core Java"], "weaknesses": ["Limited experience with cloud platforms"], "red_flags": [], "cultural_fit_indicators": ["Remote work options"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Modern technologies", "Team management"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.025634765625, "processed_at": "2025-07-12T13:59:08.763296", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_15.pdf", "file_size": 0, "word_count": 318, "success": true, "error": null}, "summary": "Customer focused individual with 12.5 years of professional experience in Software Design Development."}, {"id": "resume_7", "filename": "Profile_16.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 70.0, "keywords_match": 43.2, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Containerization (Docker, Kubernetes)", "CI/CD pipelines", "Microservices architecture"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Computer Science or related field"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Strong problem-solving and communication skills"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical questions", "Problem-solving skills"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 19.338947057724, "processed_at": "2025-07-12T13:59:08.770336", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_16.pdf", "file_size": 0, "word_count": 120, "success": true, "error": null}, "summary": "Senior software engineer with experience in distributed systems and software engineering"}, {"id": "resume_8", "filename": "Profile_17.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 6.25, "experience_score": 90.0, "education_score": 20.0, "keywords_match": 89.8, "overall_fit": 88.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has relevant experience, but lacks some required skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["AWS"], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 6.2}, "experience_analysis": {"matching_experience": ["9 years 1 month Software Engineering Manager, AWS Bedrock March 2024 - Present", "2 years 3 months Senior Software Development Engineer November 2021 - December 2022"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Computer Science or related field"], "education_level": "BASIC"}, "assessment": {"strengths": ["Experience with cloud platforms (AWS, Azure)", "Familiarity with version control (Git)"], "weaknesses": ["Lack of experience with JavaScript", "No experience with CI/CD pipelines"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of experience with JavaScript", "No experience with CI/CD pipelines"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 17.715458869934082, "processed_at": "2025-07-12T13:59:08.775922", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_17.pdf", "file_size": 0, "word_count": 150, "success": true, "error": null}, "summary": "Software Engineering Manager with 9 years of experience in AWS and cloud platforms."}, {"id": "resume_9", "filename": "Profile_14.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 30.0, "education_score": 0, "keywords_match": 14.0, "overall_fit": 38.0, "growth_potential": 25.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills and experience", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "JavaScript", "React", "Node.js", "Django", "PostgreSQL", "MongoDB", "AWS", "Azure", "<PERSON>er", "Kubernetes", "Git", "Microservices", "Agile", "Scrum", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Develop and maintain web applications", "Collaborate with cross-functional teams", "Write clean, maintainable code", "Participate in code reviews", "Troubleshoot and debug applications", "Stay updated with latest technologies"], "experience_gaps": ["Experience with containerization (<PERSON><PERSON>, Kubernetes)", "Knowledge of CI/CD pipelines", "Experience with microservices architecture"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 19.397598028182983, "processed_at": "2025-07-12T13:59:08.783018", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Resume Parser\\resumes\\Profile_14.pdf", "file_size": 0, "word_count": 218, "success": true, "error": null}, "summary": ""}]}