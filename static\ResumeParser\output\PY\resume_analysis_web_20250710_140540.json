{"meta": {"version": "1.0", "exported_at": "2025-07-10T14:05:40.403881", "total_candidates": 23, "successful_analyses": 23, "failed_analyses": 0, "job_description": "\nPython & MySQL Developer - Fresher\n📍 Location: Vijayawada, India\n🕒 Job Type: Full-Time | Entry-Level\n🌟 About the Role\nWe're looking for a passionate and inquisitive fresher eager to kick-start their journey in backend development and data-driven applications. If you're comfortable navigating Python’s data structures and dabbling in MySQL queries, this opportunity is your perfect launchpad!\n\n🚀 Key Responsibilities\nBuild and manage backend logic using Python with a focus on clean data structures\n\nWrite efficient queries to interact with MySQL databases for CRUD operations\n\nCollaborate with frontend developers to integrate APIs and ensure seamless data flow\n\nDebug and optimize backend code for performance and scalability\n\nDocument processes and assist in deployment pipelines\n\n🧠 Required Skills\nSolid understanding of Python fundamentals including lists, dictionaries, sets, tuples, and OOP concepts\n\nFamiliarity with basic MySQL queries, joins, and indexing\n\nExposure to version control systems like Git\n\nGood problem-solving and algorithmic thinking\n\n\n🤝 What We Offer\nMentorship from senior developers\n\nReal-world projects to build your portfolio\n\nA friendly, collaborative work culture\n\nOpportunities for growth into full-stack development roles\n", "processed_at": "2025-07-10T14:05:40.400661"}, "summary_statistics": {"average_score": 10.14, "score_distribution": {"excellent": 0, "good": 1, "average": 1, "below_average": 21}, "recommendations": {"HIRE": 1, "CONSIDER": 0, "REJECT": 22}, "processing_time": 70.54}, "candidates": [{"id": "resume_1", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 5, "experience_score": 95.0, "education_score": 40.0, "keywords_match": 68.0, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience and skills match the job requirements.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Helping Google ship code", "Full Stack Engineer"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Spearheaded the development of a Policy Compliance Management System"], "education_level": "BASIC"}, "assessment": {"strengths": ["Problem-solving and algorithmic thinking"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Friendly, collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.671790599822998, "processed_at": "2025-07-10T14:05:40.255713", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Experienced software engineer with a strong background in Python and MySQL development."}, {"id": "resume_2", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 25.0, "education_score": 0, "keywords_match": 1.3333333333333321, "overall_fit": 33.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the job requirements", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 9.129201889038086, "processed_at": "2025-07-10T14:05:40.264749", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_3", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 20.0, "keywords_match": 65.33333333333333, "overall_fit": 58.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Candi<PERSON> has relevant education but lacks experience in required skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Solid understanding of Python fundamentals"], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Good problem-solving and algorithmic thinking"], "weaknesses": ["Lack of experience in backend development and data-driven applications"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Data Structures", "Languages"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 10.144487142562866, "processed_at": "2025-07-10T14:05:40.268754", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": "Software Development Manager with 6 years of experience, seeking entry-level position in backend development and data-driven applications."}, {"id": "resume_4", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 59.38, "skills_match": 71.66666666666666, "experience_score": 85.0, "education_score": 0, "keywords_match": 19.333333333333332, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON>k of required skills and experience for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Exposure to version control systems like Git"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 19.823106288909912, "processed_at": "2025-07-10T14:05:40.281796", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": ""}, {"id": "resume_5", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 95.0, "education_score": 0, "keywords_match": 66.66666666666667, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in network analysis and data-driven applications, but limited exposure to OOP concepts and basic MySQL queries.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Network path discovery and analysis", "Link inference in large networks based on incomplete data"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Data driven, lean startup practitioner"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Network analysis", "Data-driven applications"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.31264066696167, "processed_at": "2025-07-10T14:05:40.293995", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of management experience and 17 years of development experience."}, {"id": "resume_6", "filename": "Profile_17.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 0, "keywords_match": 31.333333333333332, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Not enough relevant experience for a junior position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["9 years experience in software engineering"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong problem-solving skills"], "weaknesses": ["Lack of relevant backend development experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development skills", "Problem-solving abilities"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.564495086669922, "processed_at": "2025-07-10T14:05:40.297998", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_17.pdf", "file_size": 0, "word_count": 150, "success": true, "error": null}, "summary": "Experienced software engineer with 9 years of experience in AWS."}, {"id": "resume_7", "filename": "Profile_15.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 85.0, "education_score": 60.0, "keywords_match": 41.33333333333333, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Candi<PERSON> has relevant experience but lacks specific backend development skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["12.5 years of professional experience in Software Design"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.E. (Electronics Telecommunications)"], "education_level": "BASIC"}, "assessment": {"strengths": ["Customer focused individual", "Strong understanding of Core Java, Web Services"], "weaknesses": ["Lack of specific backend development skills"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Specifically highlight customer-focused skills and software design experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.761013507843018, "processed_at": "2025-07-10T14:05:40.304526", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_15.pdf", "file_size": 0, "word_count": 318, "success": true, "error": null}, "summary": "Customer focused individual with 12.5 years of professional experience in Software Design"}, {"id": "resume_8", "filename": "Profile_16.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 0, "keywords_match": 31.333333333333332, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["13 years 11 months as Manager, Software Development"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["<PERSON><PERSON><PERSON><PERSON>, Computer Science from Harvard University"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.803074598312378, "processed_at": "2025-07-10T14:05:40.308524", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_16.pdf", "file_size": 0, "word_count": 120, "success": true, "error": null}, "summary": null}, {"id": "resume_9", "filename": "Profile_18.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 30.0}, "recommendation": {"decision": "REJECT", "reason": "Not suitable for the role due to lack of relevant skills and experience.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Version control systems like Git"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor of Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Lack of relevant experience in backend development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.711257696151733, "processed_at": "2025-07-10T14:05:40.312121", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_18.pdf", "file_size": 0, "word_count": 118, "success": true, "error": null}, "summary": ""}, {"id": "resume_10", "filename": "Profile_14.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 40.0, "education_score": 0, "keywords_match": 11.333333333333332, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Candi<PERSON> has some relevant training expertise and project management skills, but lacks direct experience in backend development.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["NLP practitioner", "Corporate training experience"], "experience_gaps": ["No direct experience in backend development or data-driven applications"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["M-Technology Business Development Manager"], "education_level": "BASIC"}, "assessment": {"strengths": ["Training expertise", "Project management skills"], "weaknesses": ["Lack of relevant work experience"], "red_flags": [], "cultural_fit_indicators": ["Friendly, collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Relevant work experience", "Training expertise"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.916649580001831, "processed_at": "2025-07-10T14:05:40.317128", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_14.pdf", "file_size": 0, "word_count": 218, "success": true, "error": null}, "summary": "Freelance facilitator with 6+ years of experience in corporate training and recruitment."}, {"id": "resume_11", "filename": "Profile_19.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 45.0, "education_score": 0, "keywords_match": 74.0, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks relevant skills and experience for the role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Exposure to version control systems like Git"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Proficient in building distributed, cloud native and server-side applications"], "weaknesses": ["Lack of experience with clean data structures"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Clean data structures", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.338319778442383, "processed_at": "2025-07-10T14:05:40.326151", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_19.pdf", "file_size": 0, "word_count": 546, "success": true, "error": null}, "summary": "Programmer, architect and technical lead with expertise in C++, Golang, Rust, Linux."}, {"id": "resume_12", "filename": "Profile_2.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 90.0, "keywords_match": 76.66666666666667, "overall_fit": 78.0, "growth_potential": 85.0}, "recommendation": {"decision": "REJECT", "reason": "Strong educational background and relevant experience, but limited skills in required areas.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["React Js Developer", "Software Engineer <PERSON><PERSON>"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Bachelor of Technology - BTech, Computer Science"], "education_level": "ADVANCED"}, "assessment": {"strengths": ["Problem-solving skills", "Algorithmic thinking"], "weaknesses": ["Limited experience in backend development"], "red_flags": [], "cultural_fit_indicators": ["Friendly work culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Backend development", "Data structures"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.69557785987854, "processed_at": "2025-07-10T14:05:40.329776", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_2.pdf", "file_size": 0, "word_count": 119, "success": true, "error": null}, "summary": "Fresh candidate with strong educational background and relevant experience in React Js development."}, {"id": "resume_13", "filename": "Profile_20.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 70.0, "education_score": 30.0, "keywords_match": 40.0, "overall_fit": 58.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant HR experience, but lacks backend development skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Human Resources experience"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["HR professional experience"], "education_level": "BASIC"}, "assessment": {"strengths": ["Fast-paced environment skills", "E2E Recruitment skills"], "weaknesses": ["Lack of backend development experience"], "red_flags": [], "cultural_fit_indicators": ["Collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development skills", "HR experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.46415090560913, "processed_at": "2025-07-10T14:05:40.334795", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_20.pdf", "file_size": 0, "word_count": 232, "success": true, "error": null}, "summary": "HR professional with 2.5 years of experience in IT and non-IT industry"}, {"id": "resume_14", "filename": "Profile_4.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 45.0, "education_score": 0, "keywords_match": 12.666666666666666, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON><PERSON> required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Backend development experience"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Lack of backend development experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.260214805603027, "processed_at": "2025-07-10T14:05:40.341309", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_4.pdf", "file_size": 0, "word_count": 419, "success": true, "error": null}, "summary": ""}, {"id": "resume_15", "filename": "Profile_5.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 70.0, "keywords_match": 74.0, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical background, but some gaps in required skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Leadership experience", "Mentorship roles"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.Tech in Electronics Engineering from IIT BHU"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Problem-solving skills", "Data management expertise"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Data management", "Problem-solving"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.832213640213013, "processed_at": "2025-07-10T14:05:40.346316", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_5.pdf", "file_size": 0, "word_count": 290, "success": true, "error": null}, "summary": "Transformative journey as a Member of Technical Staff at Oracle with expertise in Apache Kafka and data processing projects."}, {"id": "resume_16", "filename": "Profile_3.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 20.0, "education_score": 0, "keywords_match": 2.666666666666666, "overall_fit": 33.0, "growth_potential": 50.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills and experience for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.2075035572052, "processed_at": "2025-07-10T14:05:40.351842", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_3.pdf", "file_size": 0, "word_count": 289, "success": true, "error": null}, "summary": ""}, {"id": "resume_17", "filename": "Profile_21.pdf", "candidate_name": "Ka<PERSON>ya E.", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 0, "keywords_match": 10.0, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Strong HR background, but lacks relevant technical skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["IT hiring experience", "Recruiting strategies implementation"], "experience_gaps": ["Backend development experience"], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Easwari Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": ["HR Talent Acquisition expertise"], "weaknesses": ["Lack of backend development experience"], "red_flags": [], "cultural_fit_indicators": ["Collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills", "Recruiting strategies"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.856985807418823, "processed_at": "2025-07-10T14:05:40.355847", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_21.pdf", "file_size": 0, "word_count": 169, "success": true, "error": null}, "summary": "HR Talent Acquisition professional with IT hiring experience and recruiting strategies expertise."}, {"id": "resume_18", "filename": "Profile_7.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 95.0, "education_score": 0, "keywords_match": 92.66666666666667, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong foundation in data structures and algorithms, but limited experience in backend development.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong foundation in data structures and algorithms", "Passionate competitive programmer"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.355123043060303, "processed_at": "2025-07-10T14:05:40.365177", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_7.pdf", "file_size": 0, "word_count": 602, "success": true, "error": null}, "summary": "Passionate competitive programmer with a strong foundation in data structures and algorithms."}, {"id": "resume_19", "filename": "Profile_6.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 70.0, "keywords_match": 76.66666666666667, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks direct experience in backend development and data-driven applications.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["National Talent Search Examination", "Finalist at American Express Codestreet 20 Hackathon"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Generative AI", "Data"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Data-driven applications"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.026081323623657, "processed_at": "2025-07-10T14:05:40.372714", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_6.pdf", "file_size": 0, "word_count": 524, "success": true, "error": null}, "summary": "Senior Software Engineer with experience building software solutions, technical advocacy, generative AI, and more."}, {"id": "resume_20", "filename": "Profile_8.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 45.0, "education_score": 20.0, "keywords_match": 76.66666666666667, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks relevant skills and experience for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Version control systems like Git"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Programming Competition CODIFICTION"], "education_level": "BASIC"}, "assessment": {"strengths": ["Good problem-solving and algorithmic thinking"], "weaknesses": ["Lack of experience in backend development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.336176633834839, "processed_at": "2025-07-10T14:05:40.379793", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_8.pdf", "file_size": 0, "word_count": 435, "success": true, "error": null}, "summary": "Freshers with strong programming skills and good problem-solving abilities."}, {"id": "resume_21", "filename": "Profile_9.pdf", "candidate_name": "Top Skills", "scores": {"final_score": 26.67, "skills_match": 33.33333333333333, "experience_score": 85.0, "education_score": 20.0, "keywords_match": 68.0, "overall_fit": 58.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant experience, but limited skills match", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python"], "missing_skills": ["MySQL", "Git"], "skill_match_percentage": 33.3}, "experience_analysis": {"matching_experience": ["Ex- Microsoft", "Ex- Amazon"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Innovation", "Scalability"], "weaknesses": ["Lack of experience in backend development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Python fundamentals"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.174654960632324, "processed_at": "2025-07-10T14:05:40.386468", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_9.pdf", "file_size": 0, "word_count": 383, "success": true, "error": null}, "summary": "Software Engineer with experience in Android and distributed systems"}, {"id": "resume_22", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 62.03, "skills_match": 71.66666666666666, "experience_score": 50.0, "education_score": 30.0, "keywords_match": 68.0, "overall_fit": 60.0, "growth_potential": 65.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks direct experience with MySQL queries and version control systems.", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology", "CGPA: 8.11"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 10.98128867149353, "processed_at": "2025-07-10T14:05:40.392061", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Dedicated and passionate coder with a strong desire to excel."}, {"id": "resume_23", "filename": "resume_sasidhar.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 85.15, "skills_match": 100, "experience_score": 65.0, "education_score": 40.0, "keywords_match": 93.33333333333333, "overall_fit": 83.0, "growth_potential": 80.0}, "recommendation": {"decision": "HIRE", "reason": "Strong foundation in backend development and version control, but limited experience with MySQL joins and indexing.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Python", "MySQL", "Git"], "missing_skills": [], "skill_match_percentage": 100.0}, "experience_analysis": {"matching_experience": ["Internship experience", "DevOps methodologies"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong grasp of version control and collaborative development using Git"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["MySQL queries", "version control systems like Git"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 15.630071878433228, "processed_at": "2025-07-10T14:05:40.400661", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume_sasidhar.pdf", "file_size": 0, "word_count": 578, "success": true, "error": null}, "summary": "Detail-oriented developer with hands-on experience designing and deploying Python-based web applications using Django."}], "top_candidates": [{"id": "resume_23", "filename": "resume_sasidhar.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 85.15, "skills_match": 100, "experience_score": 65.0, "education_score": 40.0, "keywords_match": 93.33333333333333, "overall_fit": 83.0, "growth_potential": 80.0}, "recommendation": {"decision": "HIRE", "reason": "Strong foundation in backend development and version control, but limited experience with MySQL joins and indexing.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Python", "MySQL", "Git"], "missing_skills": [], "skill_match_percentage": 100.0}, "experience_analysis": {"matching_experience": ["Internship experience", "DevOps methodologies"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong grasp of version control and collaborative development using Git"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["MySQL queries", "version control systems like Git"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 15.630071878433228, "processed_at": "2025-07-10T14:05:40.400661", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume_sasidhar.pdf", "file_size": 0, "word_count": 578, "success": true, "error": null}, "summary": "Detail-oriented developer with hands-on experience designing and deploying Python-based web applications using Django."}, {"id": "resume_22", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 62.03, "skills_match": 71.66666666666666, "experience_score": 50.0, "education_score": 30.0, "keywords_match": 68.0, "overall_fit": 60.0, "growth_potential": 65.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks direct experience with MySQL queries and version control systems.", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology", "CGPA: 8.11"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 10.98128867149353, "processed_at": "2025-07-10T14:05:40.392061", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Dedicated and passionate coder with a strong desire to excel."}, {"id": "resume_4", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 59.38, "skills_match": 71.66666666666666, "experience_score": 85.0, "education_score": 0, "keywords_match": 19.333333333333332, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON>k of required skills and experience for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Exposure to version control systems like Git"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 19.823106288909912, "processed_at": "2025-07-10T14:05:40.281796", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": ""}, {"id": "resume_21", "filename": "Profile_9.pdf", "candidate_name": "Top Skills", "scores": {"final_score": 26.67, "skills_match": 33.33333333333333, "experience_score": 85.0, "education_score": 20.0, "keywords_match": 68.0, "overall_fit": 58.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant experience, but limited skills match", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python"], "missing_skills": ["MySQL", "Git"], "skill_match_percentage": 33.3}, "experience_analysis": {"matching_experience": ["Ex- Microsoft", "Ex- Amazon"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Innovation", "Scalability"], "weaknesses": ["Lack of experience in backend development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Python fundamentals"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.174654960632324, "processed_at": "2025-07-10T14:05:40.386468", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_9.pdf", "file_size": 0, "word_count": 383, "success": true, "error": null}, "summary": "Software Engineer with experience in Android and distributed systems"}, {"id": "resume_1", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 5, "experience_score": 95.0, "education_score": 40.0, "keywords_match": 68.0, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience and skills match the job requirements.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Helping Google ship code", "Full Stack Engineer"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Spearheaded the development of a Policy Compliance Management System"], "education_level": "BASIC"}, "assessment": {"strengths": ["Problem-solving and algorithmic thinking"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Friendly, collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.671790599822998, "processed_at": "2025-07-10T14:05:40.255713", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Experienced software engineer with a strong background in Python and MySQL development."}, {"id": "resume_2", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 25.0, "education_score": 0, "keywords_match": 1.3333333333333321, "overall_fit": 33.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the job requirements", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 9.129201889038086, "processed_at": "2025-07-10T14:05:40.264749", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_3", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 20.0, "keywords_match": 65.33333333333333, "overall_fit": 58.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Candi<PERSON> has relevant education but lacks experience in required skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Solid understanding of Python fundamentals"], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Good problem-solving and algorithmic thinking"], "weaknesses": ["Lack of experience in backend development and data-driven applications"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Data Structures", "Languages"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 10.144487142562866, "processed_at": "2025-07-10T14:05:40.268754", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": "Software Development Manager with 6 years of experience, seeking entry-level position in backend development and data-driven applications."}, {"id": "resume_5", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 95.0, "education_score": 0, "keywords_match": 66.66666666666667, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in network analysis and data-driven applications, but limited exposure to OOP concepts and basic MySQL queries.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Network path discovery and analysis", "Link inference in large networks based on incomplete data"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Data driven, lean startup practitioner"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Network analysis", "Data-driven applications"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.31264066696167, "processed_at": "2025-07-10T14:05:40.293995", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of management experience and 17 years of development experience."}, {"id": "resume_6", "filename": "Profile_17.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 0, "keywords_match": 31.333333333333332, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Not enough relevant experience for a junior position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["9 years experience in software engineering"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong problem-solving skills"], "weaknesses": ["Lack of relevant backend development experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development skills", "Problem-solving abilities"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.564495086669922, "processed_at": "2025-07-10T14:05:40.297998", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_17.pdf", "file_size": 0, "word_count": 150, "success": true, "error": null}, "summary": "Experienced software engineer with 9 years of experience in AWS."}, {"id": "resume_7", "filename": "Profile_15.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 85.0, "education_score": 60.0, "keywords_match": 41.33333333333333, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Candi<PERSON> has relevant experience but lacks specific backend development skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["12.5 years of professional experience in Software Design"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.E. (Electronics Telecommunications)"], "education_level": "BASIC"}, "assessment": {"strengths": ["Customer focused individual", "Strong understanding of Core Java, Web Services"], "weaknesses": ["Lack of specific backend development skills"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Specifically highlight customer-focused skills and software design experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.761013507843018, "processed_at": "2025-07-10T14:05:40.304526", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_15.pdf", "file_size": 0, "word_count": 318, "success": true, "error": null}, "summary": "Customer focused individual with 12.5 years of professional experience in Software Design"}]}