{"meta": {"version": "1.0", "exported_at": "2025-07-12T06:24:40.053930", "total_candidates": 23, "successful_analyses": 23, "failed_analyses": 0, "job_description": "\nSenior Android Engineer - Cloud-Integrated Systems\n🏢 Location: Hybrid (Vijayawada / Remote)\n🕓 Employment Type: Full-time\n\n✨ Role Summary\nWe're seeking a highly skilled Android Engineer with strong experience in developing scalable mobile applications and integrating with distributed backend systems powered by Microsoft Azure. This role demands technical excellence, a deep understanding of system architecture, and hands-on experience with Azure Resource Manager for automating infrastructure tasks.\n\n🔧 Responsibilities\n- Design and develop advanced Android applications that communicate with distributed cloud services\n- Collaborate with backend teams to integrate mobile experiences with microservices and RESTful APIs\n- Automate deployment and provisioning using Azure Resource Manager templates and tooling\n- Optimize performance across distributed systems to support large-scale user bases\n- Lead architectural decisions and code reviews to maintain high engineering standards\n- Monitor app health, implement telemetry, and debug cross-platform issues\n\n💡 Required Skills\n- Android Development: Strong proficiency in Kotlin/Java, Jetpack libraries, UI/UX optimization, and Play Store deployment\n- Distributed Systems: Experience with scalable architecture, async communication, load balancing, and fault tolerance\n- Azure Resource Manager: Expertise in ARM templates, policy creation, role-based access control (RBAC), and CI/CD integrations\n\n🚀 Preferred Qualifications\n- Knowledge of container orchestration (Docker/Kubernetes)\n- Familiarity with service mesh patterns and event-driven architectures\n- Experience with monitoring tools like Azure Monitor, Application Insights\n- Contributions to open-source projects or Android communities\n\n🌈 Perks & Benefits\n- Flexible work hours and remote-first culture\n- Continuous learning allowance and certifications\n- Stock options or performance bonuses\n- Mentorship programs and tech talks\n\n", "processed_at": "2025-07-12T06:24:40.044933"}, "summary_statistics": {"average_score": 4.7, "score_distribution": {"excellent": 0, "good": 0, "average": 0, "below_average": 23}, "recommendations": {"HIRE": 0, "CONSIDER": 0, "REJECT": 23}, "processing_time": 75.1}, "candidates": [{"id": "resume_1", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 95.0, "education_score": 0, "keywords_match": 78.23529411764706, "overall_fit": 91.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills, relevant experience, but missing required skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["<PERSON>er", "CI/CD"], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Kubernetes", "Android", "Microservices"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": ["Senior Android Engineer - Cloud-Integrated Systems", "SWE @Google", "BNY Full Stack Engineer"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Technical excellence", "Deep understanding of system architecture"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Flexible work hours and remote-first culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical excellence", "System architecture"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 10.275153636932373, "processed_at": "2025-07-12T06:24:39.779483", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Senior Android Engineer with strong experience in developing scalable mobile applications and integrating with distributed backend systems powered by Microsoft Azure"}, {"id": "resume_2", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 5, "education_score": 0, "keywords_match": 0, "overall_fit": 3, "growth_potential": 0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the job requirements", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 8.988754510879517, "processed_at": "2025-07-12T06:24:39.792581", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_3", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 40.0, "keywords_match": 42.35294117647059, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Software Development Engineer II", "Software Development Manager"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science", "Bachelor of Engineering (BE), Computer Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.227097749710083, "processed_at": "2025-07-12T06:24:39.803137", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": ""}, {"id": "resume_4", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 85.0, "education_score": 0, "keywords_match": 67.05882352941177, "overall_fit": 68.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has relevant experience and skills, but lacks direct match for the job requirements.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java", "Microservices"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "CI/CD"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": ["Previous experience with D.E. Shaw Co.", "Interned at Goldman Sachs as an SDE intern"], "experience_gaps": ["Lack of direct experience in Android Engineering and Cloud-Integrated Systems"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 22.12780785560608, "processed_at": "2025-07-12T06:24:39.819025", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": ""}, {"id": "resume_5", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 95.0, "education_score": 0, "keywords_match": 71.76470588235294, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in Android Development, Distributed Systems, and Azure Resource Manager, but lacks some required skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java", "Android"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Microservices", "CI/CD"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Scalable architecture", "Async communication", "Load balancing", "Fault tolerance"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Data driven", "Lean startup practitioner"], "weaknesses": ["Lack of experience with RESTful APIs"], "red_flags": [], "cultural_fit_indicators": ["Flexible work hours and remote-first culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Scalable architecture", "Async communication", "Load balancing", "Fault tolerance"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.192298412322998, "processed_at": "2025-07-12T06:24:39.839344", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of experience in management and 17 years in development of commercial software."}, {"id": "resume_6", "filename": "Profile_14.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 30.0, "education_score": 0, "keywords_match": 4.705882352941177, "overall_fit": 18.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON>k of required skills and experience for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.459980487823486, "processed_at": "2025-07-12T06:24:39.846865", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_14.pdf", "file_size": 0, "word_count": 218, "success": true, "error": null}, "summary": ""}, {"id": "resume_7", "filename": "Profile_17.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 12.5, "experience_score": 90.0, "education_score": 0, "keywords_match": 63.529411764705884, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical expertise, but limited Azure Resource Manager experience.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 12.5}, "experience_analysis": {"matching_experience": ["Experience with distributed systems to support large-scale user bases"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Technical expertise", "Leadership experience"], "weaknesses": ["Limited Azure Resource Manager experience"], "red_flags": [], "cultural_fit_indicators": ["Flexible work hours and remote-first culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Azure Resource Manager", "Distributed Systems"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 18.499578714370728, "processed_at": "2025-07-12T06:24:39.852218", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_17.pdf", "file_size": 0, "word_count": 150, "success": true, "error": null}, "summary": "Senior Software Development Engineer with 9 years of experience in software engineering and leadership roles."}, {"id": "resume_8", "filename": "Profile_18.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 30.0, "education_score": 0, "keywords_match": 3.529411764705883, "overall_fit": 18.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate lacks required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor of Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": ["Candidate's strengths based on resume"], "weaknesses": ["Gaps or weaknesses in candidate's profile"], "red_flags": [], "cultural_fit_indicators": ["Indicators of cultural fit"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Specific areas to focus on during interview"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.182486772537231, "processed_at": "2025-07-12T06:24:39.857221", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_18.pdf", "file_size": 0, "word_count": 118, "success": true, "error": null}, "summary": "Summary of candidate's profile"}, {"id": "resume_9", "filename": "Profile_15.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 95.0, "education_score": 70.0, "keywords_match": 78.23529411764706, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has relevant experience but lacks specific skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java", "Microservices"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "CI/CD"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.E. (Electronics Telecommunications)"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Customer focused individual with 12.5 years of professional experience in Software Design"], "weaknesses": ["Gaps in technical skills for Azure Resource Manager"], "red_flags": [], "cultural_fit_indicators": ["Flexible work hours and remote-first culture"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills for Azure Resource Manager"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 18.971416234970093, "processed_at": "2025-07-12T06:24:39.865221", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_15.pdf", "file_size": 0, "word_count": 318, "success": true, "error": null}, "summary": "Customer focused individual with 12.5 years of professional experience in Software Design"}, {"id": "resume_10", "filename": "Profile_19.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 30.0, "skills_match": 37.5, "experience_score": 95.0, "education_score": 0, "keywords_match": 69.41176470588235, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills, but missing required Azure expertise and Android development experience.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java", "<PERSON>er", "Kubernetes"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 37.5}, "experience_analysis": {"matching_experience": ["Experience with scalable architecture", "Expertise in async communication", "Experience with monitoring tools like Azure Monitor, Application Insights"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Technical lead, Author of Learning Boost C++ Libraries"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Azure Resource Manager", "Android Development"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.905900001525879, "processed_at": "2025-07-12T06:24:39.876212", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_19.pdf", "file_size": 0, "word_count": 546, "success": true, "error": null}, "summary": "Programmer, architect and technical lead with expertise in distributed systems, Kubernetes, Envoy, and C++"}, {"id": "resume_11", "filename": "Profile_2.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 20.0, "keywords_match": 63.529411764705884, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills and experience in distributed systems and Azure Resource Manager.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["React Js Developer"], "experience_gaps": ["Expertise in automating infrastructure tasks", "Optimize performance across distributed systems", "Lead architectural decisions and code reviews"], "experience_level": "MID"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 19.07934260368347, "processed_at": "2025-07-12T06:24:39.884137", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_2.pdf", "file_size": 0, "word_count": 119, "success": true, "error": null}, "summary": ""}, {"id": "resume_12", "filename": "Profile_16.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 0, "keywords_match": 63.529411764705884, "overall_fit": 88.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate lacks required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Experience with scalable architecture", "async communication", "load balancing", "fault tolerance"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["A.B., Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Technical excellence", "deep understanding of system architecture"], "weaknesses": ["Lack of direct experience with Android Development and Azure Resource Manager"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of direct experience with Android Development and Azure Resource Manager"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.189047813415527, "processed_at": "2025-07-12T06:24:39.898409", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_16.pdf", "file_size": 0, "word_count": 120, "success": true, "error": null}, "summary": "Highly experienced software engineer with strong technical background but limited experience in Android Development and Azure Resource Manager"}, {"id": "resume_13", "filename": "Profile_20.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the job requirements", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 18.45143175125122, "processed_at": "2025-07-12T06:24:39.907413", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_20.pdf", "file_size": 0, "word_count": 232, "success": true, "error": null}, "summary": ""}, {"id": "resume_14", "filename": "Profile_21.pdf", "candidate_name": "Ka<PERSON>ya E.", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON>k of required skills and experience for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Easwari Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.029215335845947, "processed_at": "2025-07-12T06:24:39.913413", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_21.pdf", "file_size": 0, "word_count": 169, "success": true, "error": null}, "summary": ""}, {"id": "resume_15", "filename": "Profile_4.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 15.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 30.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior Android Engineer - Cloud-Integrated Systems"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.65343475341797, "processed_at": "2025-07-12T06:24:39.922415", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_4.pdf", "file_size": 0, "word_count": 419, "success": true, "error": null}, "summary": ""}, {"id": "resume_16", "filename": "Profile_5.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 70.0, "keywords_match": 89.70588235294117, "overall_fit": 88.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in distributed systems, but missing container orchestration skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Scalable Architecture", "Async Communication", "<PERSON><PERSON>", "Fault Tolerance"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.Tech in Electronics Engineering from IIT BHU"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Robust problem-solving", "High-volume data workloads management"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Flexible work hours and remote-first culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Leadership experiences"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.892822742462158, "processed_at": "2025-07-12T06:24:39.932749", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_5.pdf", "file_size": 0, "word_count": 290, "success": true, "error": null}, "summary": "Transformative journey as a Member of Technical Staff at Oracle with expertise in Apache Kafka and Kafka Streams."}, {"id": "resume_17", "filename": "Profile_3.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON>k of required skills and experience for the position.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior Android Engineer - Cloud-Integrated Systems"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.963119506835938, "processed_at": "2025-07-12T06:24:39.941919", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_3.pdf", "file_size": 0, "word_count": 289, "success": true, "error": null}, "summary": ""}, {"id": "resume_18", "filename": "Profile_8.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 12.5, "experience_score": 45.0, "education_score": 20.0, "keywords_match": 77.05882352941177, "overall_fit": 53.0, "growth_potential": 30.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON>k of required skills and experience for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 12.5}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Merit Certificate for Academic Excellence in Computer Science", "NALANDA SCHOLARSHIP MERIT CERTIFICATE"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Required skills and experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.633476972579956, "processed_at": "2025-07-12T06:24:39.959773", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_8.pdf", "file_size": 0, "word_count": 435, "success": true, "error": null}, "summary": "Summary of candidate's profile"}, {"id": "resume_19", "filename": "Profile_7.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 95.0, "education_score": 0, "keywords_match": 95.88235294117646, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong foundation in data structures and algorithms, but limited experience with Android development and Azure Resource Manager.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience in leading architectural decisions and code reviews"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong foundation in data structures and algorithms", "Passionate competitive programmer"], "weaknesses": ["Limited experience with UI/UX optimization and Play Store deployment"], "red_flags": [], "cultural_fit_indicators": ["Flexible work hours and remote-first culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Data structures and algorithms", "Android development", "Azure Resource Manager"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.825730085372925, "processed_at": "2025-07-12T06:24:39.976188", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_7.pdf", "file_size": 0, "word_count": 602, "success": true, "error": null}, "summary": "Passionate competitive programmer with a strong foundation in data structures and algorithms."}, {"id": "resume_20", "filename": "Profile_6.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 0, "keywords_match": 97.05882352941177, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills, but missing required Azure expertise", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Technical expertise, leadership experience"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Flexible work hours and remote-first culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical challenges", "Leadership experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.286860466003418, "processed_at": "2025-07-12T06:24:39.994154", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_6.pdf", "file_size": 0, "word_count": 524, "success": true, "error": null}, "summary": "Senior Software Engineer with experience in Android development, distributed systems, and leadership roles"}, {"id": "resume_21", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 0, "skills_match": 17.5, "experience_score": 50.0, "education_score": 20.0, "keywords_match": 74.70588235294117, "overall_fit": 75.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Android"], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Microservices", "CI/CD"], "skill_match_percentage": 12.5}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology"], "education_level": "BASIC"}, "assessment": {"strengths": ["Dedicated and passionate coder", "Technologically adept"], "weaknesses": ["Gaps in technical expertise"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical expertise", "Relevant experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.83840560913086, "processed_at": "2025-07-12T06:24:40.007930", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Summary of candidate's profile"}, {"id": "resume_22", "filename": "Profile_9.pdf", "candidate_name": "Top Skills", "scores": {"final_score": 30.0, "skills_match": 37.5, "experience_score": 95.0, "education_score": 70.0, "keywords_match": 100, "overall_fit": 91.0, "growth_potential": 92.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in Android, Distributed Systems, and Azure Resource Manager, but limited experience with container orchestration and service mesh patterns.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java", "Azure", "Android"], "missing_skills": ["<PERSON><PERSON><PERSON>", "<PERSON>er", "Kubernetes", "Microservices", "CI/CD"], "skill_match_percentage": 37.5}, "experience_analysis": {"matching_experience": ["Senior Member of Technical Staff at Salesforce", "Software Development Engineer at Microsoft"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Electrical, Electronics, and Communications Engineering from PSG College of Technology"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Innovation", "Scalability"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Flexible work hours and remote-first culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Innovation", "Scalability"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.59427833557129, "processed_at": "2025-07-12T06:24:40.025936", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_9.pdf", "file_size": 0, "word_count": 383, "success": true, "error": null}, "summary": "Senior Software Engineer with expertise in Android, Distributed Systems, and Azure Resource Manager."}, {"id": "resume_23", "filename": "resume_sasidhar.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 5, "experience_score": 45.0, "education_score": 0, "keywords_match": 82.94117647058823, "overall_fit": 58.0, "growth_potential": 30.0}, "recommendation": {"decision": "REJECT", "reason": "Skills and experience do not match job requirements.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.673527956008911, "processed_at": "2025-07-12T06:24:40.041858", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume_sasidhar.pdf", "file_size": 0, "word_count": 578, "success": true, "error": null}, "summary": ""}], "top_candidates": [{"id": "resume_10", "filename": "Profile_19.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 30.0, "skills_match": 37.5, "experience_score": 95.0, "education_score": 0, "keywords_match": 69.41176470588235, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills, but missing required Azure expertise and Android development experience.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java", "<PERSON>er", "Kubernetes"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 37.5}, "experience_analysis": {"matching_experience": ["Experience with scalable architecture", "Expertise in async communication", "Experience with monitoring tools like Azure Monitor, Application Insights"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Technical lead, Author of Learning Boost C++ Libraries"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Azure Resource Manager", "Android Development"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.905900001525879, "processed_at": "2025-07-12T06:24:39.876212", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_19.pdf", "file_size": 0, "word_count": 546, "success": true, "error": null}, "summary": "Programmer, architect and technical lead with expertise in distributed systems, Kubernetes, Envoy, and C++"}, {"id": "resume_22", "filename": "Profile_9.pdf", "candidate_name": "Top Skills", "scores": {"final_score": 30.0, "skills_match": 37.5, "experience_score": 95.0, "education_score": 70.0, "keywords_match": 100, "overall_fit": 91.0, "growth_potential": 92.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in Android, Distributed Systems, and Azure Resource Manager, but limited experience with container orchestration and service mesh patterns.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java", "Azure", "Android"], "missing_skills": ["<PERSON><PERSON><PERSON>", "<PERSON>er", "Kubernetes", "Microservices", "CI/CD"], "skill_match_percentage": 37.5}, "experience_analysis": {"matching_experience": ["Senior Member of Technical Staff at Salesforce", "Software Development Engineer at Microsoft"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Electrical, Electronics, and Communications Engineering from PSG College of Technology"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Innovation", "Scalability"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Flexible work hours and remote-first culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Innovation", "Scalability"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.59427833557129, "processed_at": "2025-07-12T06:24:40.025936", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_9.pdf", "file_size": 0, "word_count": 383, "success": true, "error": null}, "summary": "Senior Software Engineer with expertise in Android, Distributed Systems, and Azure Resource Manager."}, {"id": "resume_1", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 95.0, "education_score": 0, "keywords_match": 78.23529411764706, "overall_fit": 91.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills, relevant experience, but missing required skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["<PERSON>er", "CI/CD"], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Kubernetes", "Android", "Microservices"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": ["Senior Android Engineer - Cloud-Integrated Systems", "SWE @Google", "BNY Full Stack Engineer"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Technical excellence", "Deep understanding of system architecture"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Flexible work hours and remote-first culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical excellence", "System architecture"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 10.275153636932373, "processed_at": "2025-07-12T06:24:39.779483", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Senior Android Engineer with strong experience in developing scalable mobile applications and integrating with distributed backend systems powered by Microsoft Azure"}, {"id": "resume_4", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 85.0, "education_score": 0, "keywords_match": 67.05882352941177, "overall_fit": 68.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has relevant experience and skills, but lacks direct match for the job requirements.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java", "Microservices"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "CI/CD"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": ["Previous experience with D.E. Shaw Co.", "Interned at Goldman Sachs as an SDE intern"], "experience_gaps": ["Lack of direct experience in Android Engineering and Cloud-Integrated Systems"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 22.12780785560608, "processed_at": "2025-07-12T06:24:39.819025", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": ""}, {"id": "resume_2", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 5, "education_score": 0, "keywords_match": 0, "overall_fit": 3, "growth_potential": 0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the job requirements", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 8.988754510879517, "processed_at": "2025-07-12T06:24:39.792581", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_3", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 40.0, "keywords_match": 42.35294117647059, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Software Development Engineer II", "Software Development Manager"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science", "Bachelor of Engineering (BE), Computer Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.227097749710083, "processed_at": "2025-07-12T06:24:39.803137", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": ""}, {"id": "resume_5", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 95.0, "education_score": 0, "keywords_match": 71.76470588235294, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in Android Development, Distributed Systems, and Azure Resource Manager, but lacks some required skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java", "Android"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Microservices", "CI/CD"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Scalable architecture", "Async communication", "Load balancing", "Fault tolerance"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Data driven", "Lean startup practitioner"], "weaknesses": ["Lack of experience with RESTful APIs"], "red_flags": [], "cultural_fit_indicators": ["Flexible work hours and remote-first culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Scalable architecture", "Async communication", "Load balancing", "Fault tolerance"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.192298412322998, "processed_at": "2025-07-12T06:24:39.839344", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of experience in management and 17 years in development of commercial software."}, {"id": "resume_6", "filename": "Profile_14.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 30.0, "education_score": 0, "keywords_match": 4.705882352941177, "overall_fit": 18.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON>k of required skills and experience for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.459980487823486, "processed_at": "2025-07-12T06:24:39.846865", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_14.pdf", "file_size": 0, "word_count": 218, "success": true, "error": null}, "summary": ""}, {"id": "resume_7", "filename": "Profile_17.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 12.5, "experience_score": 90.0, "education_score": 0, "keywords_match": 63.529411764705884, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical expertise, but limited Azure Resource Manager experience.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 12.5}, "experience_analysis": {"matching_experience": ["Experience with distributed systems to support large-scale user bases"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Technical expertise", "Leadership experience"], "weaknesses": ["Limited Azure Resource Manager experience"], "red_flags": [], "cultural_fit_indicators": ["Flexible work hours and remote-first culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Azure Resource Manager", "Distributed Systems"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 18.499578714370728, "processed_at": "2025-07-12T06:24:39.852218", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_17.pdf", "file_size": 0, "word_count": 150, "success": true, "error": null}, "summary": "Senior Software Development Engineer with 9 years of experience in software engineering and leadership roles."}, {"id": "resume_8", "filename": "Profile_18.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 30.0, "education_score": 0, "keywords_match": 3.529411764705883, "overall_fit": 18.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate lacks required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "<PERSON>er", "Kubernetes", "Android", "Microservices", "CI/CD"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor of Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": ["Candidate's strengths based on resume"], "weaknesses": ["Gaps or weaknesses in candidate's profile"], "red_flags": [], "cultural_fit_indicators": ["Indicators of cultural fit"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Specific areas to focus on during interview"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.182486772537231, "processed_at": "2025-07-12T06:24:39.857221", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_18.pdf", "file_size": 0, "word_count": 118, "success": true, "error": null}, "summary": "Summary of candidate's profile"}]}