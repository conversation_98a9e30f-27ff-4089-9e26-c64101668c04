{"meta": {"version": "1.0", "exported_at": "2025-07-12T07:14:02.691205", "total_candidates": 23, "successful_analyses": 23, "failed_analyses": 0, "job_description": "\nSenior Android Developer - Distributed Systems & Azure Automation\nLocation: Vijayawada / Remote\nType: Full-Time\nOverview:\nWe are hiring a developer with expertise in Android development, distributed systems architecture, and cloud automation using Azure Resource Manager. The role focuses exclusively on these three technical areas, emphasizing hands-on implementation and system design.\nResponsibilities:\n- Develop Android applications using best practices\n- Design and implement scalable distributed systems\n- Automate cloud infrastructure with Azure Resource Manager\nRequired Skills:\n- Android: Strong knowledge of Kotlin/Java, Android SDK, and UI development\n- Distributed Systems: Experience with system scalability, asynchronous processing, and fault tolerance\n- Azure Resource Manager: Proficiency in ARM templates and cloud resource provisioning\n", "processed_at": "2025-07-12T07:14:02.688697"}, "summary_statistics": {"average_score": 8.45, "score_distribution": {"excellent": 0, "good": 1, "average": 1, "below_average": 21}, "recommendations": {"HIRE": 1, "CONSIDER": 0, "REJECT": 22}, "processing_time": 69.61}, "candidates": [{"id": "resume_1", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 5, "experience_score": 95.0, "education_score": 0, "keywords_match": 93.63636363636364, "overall_fit": 98.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills and relevant experience for the role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Senior Android Developer - Distributed Systems & Azure Automation"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Hands-on implementation and system design expertise", "Experience in deploying 90 of Googles products"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Indicators of cultural fit"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical implementation details", "System design and scalability"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.076712369918823, "processed_at": "2025-07-12T07:14:02.559059", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Senior Android Developer with expertise in Android development, distributed systems architecture, and cloud automation using Azure Resource Manager"}, {"id": "resume_2", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 85.0, "education_score": 0, "keywords_match": 65.45454545454545, "overall_fit": 68.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Android application development", "Scalable distributed systems design and implementation", "Cloud infrastructure automation with Azure Resource Manager"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 8.878039360046387, "processed_at": "2025-07-12T07:14:02.571065", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": ""}, {"id": "resume_3", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 63.4, "skills_match": 50.0, "experience_score": 95.0, "education_score": 0, "keywords_match": 100, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in Android development, distributed systems, and Azure automation.", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["Java", "Android"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure"], "skill_match_percentage": 50.0}, "experience_analysis": {"matching_experience": ["17 years in development of commercial software"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Data driven and lean startup practitioner"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 19.55078673362732, "processed_at": "2025-07-12T07:14:02.582555", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of management experience and 17 years of software development experience."}, {"id": "resume_4", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 20.0, "keywords_match": 81.81818181818181, "overall_fit": 78.0, "growth_potential": 60.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has relevant software development experience, but lacks direct experience with Senior Android Developer role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior Android Developer role not mentioned in resume"], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong software development experience"], "weaknesses": ["<PERSON>k of direct experience with Senior Android Developer role"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["<PERSON>k of direct experience with Senior Android Developer role"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 9.109834432601929, "processed_at": "2025-07-12T07:14:02.585595", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": "Software development manager with 6 years of experience in Android and distributed systems."}, {"id": "resume_5", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 5, "education_score": 0, "keywords_match": 0, "overall_fit": 3, "growth_potential": 0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant technical experience and skills for the job requirements.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.057812213897705, "processed_at": "2025-07-12T07:14:02.593179", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_6", "filename": "Profile_15.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 95.0, "education_score": 70.0, "keywords_match": 97.27272727272727, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in software design and development, but limited expertise in Android development and Azure Resource Manager.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": ["Enterprise Integration Architecture", "SOA and Microservices"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.E. (Electronics Telecommunications)"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Customer focused individual", "Strong understanding of Core Java, Web Services (SOAP/REST)"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Enterprise Integration Architecture", "SOA and Microservices"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.196665048599243, "processed_at": "2025-07-12T07:14:02.598096", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_15.pdf", "file_size": 0, "word_count": 318, "success": true, "error": null}, "summary": "Customer focused individual with 12.5 years of professional experience in Software Design Development."}, {"id": "resume_7", "filename": "Profile_17.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 90.0, "education_score": 0, "keywords_match": 93.63636363636364, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills and relevant experience", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": ["Software Engineering Manager", "Senior Software Development Engineer"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Leadership experience, Technical expertise"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "HIGH", "interview_focus_areas": ["Leadership experience", "Technical expertise"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.902215957641602, "processed_at": "2025-07-12T07:14:02.601602", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_17.pdf", "file_size": 0, "word_count": 150, "success": true, "error": null}, "summary": "Senior Android Developer with expertise in distributed systems and Azure automation"}, {"id": "resume_8", "filename": "Profile_14.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 20.0, "education_score": 0, "keywords_match": 53.63636363636364, "overall_fit": 33.0, "growth_potential": 50.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate lacks required skills for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior Android Developer experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Lack of relevant work experience for Senior Android Developer role"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of relevant work experience", "Skill gaps"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.722907066345215, "processed_at": "2025-07-12T07:14:02.605604", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_14.pdf", "file_size": 0, "word_count": 218, "success": true, "error": null}, "summary": "Freelance Facilitator with experience in HR recruitment and corporate training"}, {"id": "resume_9", "filename": "Profile_18.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 23.0, "growth_potential": 0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior Android Developer - Distributed Systems & Azure Automation"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.168436288833618, "processed_at": "2025-07-12T07:14:02.608604", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_18.pdf", "file_size": 0, "word_count": 118, "success": true, "error": null}, "summary": ""}, {"id": "resume_10", "filename": "Profile_16.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 20.0, "keywords_match": 95.45454545454545, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong leadership experience and technical expertise, but limited experience in Android development.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Design and implement scalable distributed systems", "Automate cloud infrastructure with Azure Resource Manager"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Harvard University A.B., Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong leadership experience", "Technical expertise in software engineering"], "weaknesses": ["Limited experience in Android development"], "red_flags": [], "cultural_fit_indicators": ["Leadership skills"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Leadership skills", "Technical expertise"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.440582990646362, "processed_at": "2025-07-12T07:14:02.611110", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_16.pdf", "file_size": 0, "word_count": 120, "success": true, "error": null}, "summary": "Highly experienced software engineer with strong leadership skills and technical expertise."}, {"id": "resume_11", "filename": "Profile_19.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 95.0, "education_score": 0, "keywords_match": 94.0909090909091, "overall_fit": 88.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills but limited experience in UI development and cloud resource provisioning.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Technical lead, Agile development"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Agile development"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.886622190475464, "processed_at": "2025-07-12T07:14:02.618617", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_19.pdf", "file_size": 0, "word_count": 546, "success": true, "error": null}, "summary": "Experienced programmer, architect, and technical lead with expertise in distributed systems, cloud native applications, and server-side development."}, {"id": "resume_12", "filename": "Profile_4.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 15.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.903262615203857, "processed_at": "2025-07-12T07:14:02.625124", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_4.pdf", "file_size": 0, "word_count": 419, "success": true, "error": null}, "summary": ""}, {"id": "resume_13", "filename": "Profile_21.pdf", "candidate_name": "Ka<PERSON>ya E", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant technical experience and skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Easwari Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Gaps in technical skills"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills and experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.673086404800415, "processed_at": "2025-07-12T07:14:02.629124", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_21.pdf", "file_size": 0, "word_count": 169, "success": true, "error": null}, "summary": "HR Talent Acquisition professional with no apparent technical expertise"}, {"id": "resume_14", "filename": "Profile_2.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 20.0, "keywords_match": 55.45454545454545, "overall_fit": 78.0, "growth_potential": 60.0}, "recommendation": {"decision": "REJECT", "reason": "Candi<PERSON> has relevant experience but lacks expertise in required technical areas.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Distributed systems architecture", "Azure automation"], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Bachelor of Technology - BTech, Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong knowledge of Kotlin/Java", "Experience with React Js"], "weaknesses": ["Lack of experience in distributed systems architecture and Azure automation"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Distributed systems architecture", "Azure automation"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.559358358383179, "processed_at": "2025-07-12T07:14:02.632631", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_2.pdf", "file_size": 0, "word_count": 119, "success": true, "error": null}, "summary": "Software developer with experience in React Js and Android development."}, {"id": "resume_15", "filename": "Profile_3.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required technical skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Gaps in technical skills"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills and experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.680228233337402, "processed_at": "2025-07-12T07:14:02.637631", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_3.pdf", "file_size": 0, "word_count": 289, "success": true, "error": null}, "summary": ""}, {"id": "resume_16", "filename": "Profile_5.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 60.0, "keywords_match": 86.81818181818181, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills, but limited direct experience with Azure Resource Manager and Android development for distributed systems.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Develop Android applications using best practices", "Design and implement scalable distributed systems", "Automate cloud infrastructure with Azure Resource Manager"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Robust problem-solving", "Ability to manage high-volume data workloads", "Leadership and mentorship experience"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Leadership experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.18314266204834, "processed_at": "2025-07-12T07:14:02.642136", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_5.pdf", "file_size": 0, "word_count": 290, "success": true, "error": null}, "summary": "Transformative journey as a Member of Technical Staff at Oracle, leveraging Apache Kafka and Kafka Streams in cutting-edge data processing projects."}, {"id": "resume_17", "filename": "Profile_7.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 95.0, "education_score": 0, "keywords_match": 51.81818181818182, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong foundation in data structures and algorithms, but lacks direct experience in Android development, distributed systems architecture, and Azure automation.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong foundation in data structures and algorithms", "Passionate competitive programmer"], "weaknesses": ["Lack of experience in UI development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of experience in UI development", "Direct application of skills to the job requirements"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.026428461074829, "processed_at": "2025-07-12T07:14:02.650640", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_7.pdf", "file_size": 0, "word_count": 602, "success": true, "error": null}, "summary": "Passionate competitive programmer with a strong foundation in data structures and algorithms."}, {"id": "resume_18", "filename": "Profile_20.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 5.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Gaps or weaknesses in candidate's profile"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.278832912445068, "processed_at": "2025-07-12T07:14:02.655645", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_20.pdf", "file_size": 0, "word_count": 232, "success": true, "error": null}, "summary": "HR professional with 2.5 years of experience in IT and non IT industry"}, {"id": "resume_19", "filename": "Profile_9.pdf", "candidate_name": "Vidhyasagarp", "scores": {"final_score": 82.98, "skills_match": 75.0, "experience_score": 95.0, "education_score": 70.0, "keywords_match": 99.54545454545455, "overall_fit": 91.0, "growth_potential": 92.0}, "recommendation": {"decision": "HIRE", "reason": "Strong technical skills and relevant experience, but limited education match.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Java", "Azure", "Android"], "missing_skills": ["<PERSON><PERSON><PERSON>"], "skill_match_percentage": 75.0}, "experience_analysis": {"matching_experience": ["Senior Member of Technical Staff at Salesforce", "Software Development Engineer at Microsoft"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Electrical, Electronics, and Communications Engineering from PSG College of Technology"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Innovation", "Scalability"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Industry experience"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 14.574386596679688, "processed_at": "2025-07-12T07:14:02.661150", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_9.pdf", "file_size": 0, "word_count": 383, "success": true, "error": null}, "summary": "Senior Android Developer with expertise in distributed systems and Azure automation."}, {"id": "resume_20", "filename": "Profile_8.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 85.0, "education_score": 40.0, "keywords_match": 89.0909090909091, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Required skills not found in resume"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.19608998298645, "processed_at": "2025-07-12T07:14:02.667152", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_8.pdf", "file_size": 0, "word_count": 435, "success": true, "error": null}, "summary": "Summary of candidate's profile"}, {"id": "resume_21", "filename": "Profile_6.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 0, "keywords_match": 95.45454545454545, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in Android, Distributed Systems, and Azure Resource Manager, but lacks UI development skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Senior Software Engineer", "Samsung", "ISRO"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.901265621185303, "processed_at": "2025-07-12T07:14:02.674686", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_6.pdf", "file_size": 0, "word_count": 524, "success": true, "error": null}, "summary": "Senior Software Engineer with expertise in Android, Distributed Systems, and Azure Resource Manager."}, {"id": "resume_22", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 40.0, "education_score": 10.0, "keywords_match": 71.81818181818181, "overall_fit": 55.0, "growth_potential": 30.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Android"], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology"], "education_level": "BASIC"}, "assessment": {"strengths": ["Dedicated and passionate coder with a strong desire to excel", "Technologically adept, I offer experience with various social media platforms, office technology programs, and advanced computer skills"], "weaknesses": ["Gaps in technical expertise"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical expertise and problem-solving abilities"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.094927787780762, "processed_at": "2025-07-12T07:14:02.680044", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Summary of candidate's profile"}, {"id": "resume_23", "filename": "resume_sasidhar.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 5, "experience_score": 25.0, "education_score": 0, "keywords_match": 59.09090909090909, "overall_fit": 38.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Gaps in technical expertise"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical expertise", "Relevant experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.45315670967102, "processed_at": "2025-07-12T07:14:02.687707", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume_sasidhar.pdf", "file_size": 0, "word_count": 578, "success": true, "error": null}, "summary": "Detail-oriented developer with hands-on experience in backend development and database integration"}], "top_candidates": [{"id": "resume_19", "filename": "Profile_9.pdf", "candidate_name": "Vidhyasagarp", "scores": {"final_score": 82.98, "skills_match": 75.0, "experience_score": 95.0, "education_score": 70.0, "keywords_match": 99.54545454545455, "overall_fit": 91.0, "growth_potential": 92.0}, "recommendation": {"decision": "HIRE", "reason": "Strong technical skills and relevant experience, but limited education match.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Java", "Azure", "Android"], "missing_skills": ["<PERSON><PERSON><PERSON>"], "skill_match_percentage": 75.0}, "experience_analysis": {"matching_experience": ["Senior Member of Technical Staff at Salesforce", "Software Development Engineer at Microsoft"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Electrical, Electronics, and Communications Engineering from PSG College of Technology"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Innovation", "Scalability"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Industry experience"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 14.574386596679688, "processed_at": "2025-07-12T07:14:02.661150", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_9.pdf", "file_size": 0, "word_count": 383, "success": true, "error": null}, "summary": "Senior Android Developer with expertise in distributed systems and Azure automation."}, {"id": "resume_3", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 63.4, "skills_match": 50.0, "experience_score": 95.0, "education_score": 0, "keywords_match": 100, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in Android development, distributed systems, and Azure automation.", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["Java", "Android"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure"], "skill_match_percentage": 50.0}, "experience_analysis": {"matching_experience": ["17 years in development of commercial software"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Data driven and lean startup practitioner"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 19.55078673362732, "processed_at": "2025-07-12T07:14:02.582555", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of management experience and 17 years of software development experience."}, {"id": "resume_2", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 85.0, "education_score": 0, "keywords_match": 65.45454545454545, "overall_fit": 68.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Android application development", "Scalable distributed systems design and implementation", "Cloud infrastructure automation with Azure Resource Manager"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 8.878039360046387, "processed_at": "2025-07-12T07:14:02.571065", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": ""}, {"id": "resume_22", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 40.0, "education_score": 10.0, "keywords_match": 71.81818181818181, "overall_fit": 55.0, "growth_potential": 30.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Android"], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology"], "education_level": "BASIC"}, "assessment": {"strengths": ["Dedicated and passionate coder with a strong desire to excel", "Technologically adept, I offer experience with various social media platforms, office technology programs, and advanced computer skills"], "weaknesses": ["Gaps in technical expertise"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical expertise and problem-solving abilities"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.094927787780762, "processed_at": "2025-07-12T07:14:02.680044", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Summary of candidate's profile"}, {"id": "resume_1", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 5, "experience_score": 95.0, "education_score": 0, "keywords_match": 93.63636363636364, "overall_fit": 98.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills and relevant experience for the role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Senior Android Developer - Distributed Systems & Azure Automation"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Hands-on implementation and system design expertise", "Experience in deploying 90 of Googles products"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Indicators of cultural fit"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical implementation details", "System design and scalability"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.076712369918823, "processed_at": "2025-07-12T07:14:02.559059", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Senior Android Developer with expertise in Android development, distributed systems architecture, and cloud automation using Azure Resource Manager"}, {"id": "resume_4", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 20.0, "keywords_match": 81.81818181818181, "overall_fit": 78.0, "growth_potential": 60.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has relevant software development experience, but lacks direct experience with Senior Android Developer role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior Android Developer role not mentioned in resume"], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong software development experience"], "weaknesses": ["<PERSON>k of direct experience with Senior Android Developer role"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["<PERSON>k of direct experience with Senior Android Developer role"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 9.109834432601929, "processed_at": "2025-07-12T07:14:02.585595", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": "Software development manager with 6 years of experience in Android and distributed systems."}, {"id": "resume_5", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 5, "education_score": 0, "keywords_match": 0, "overall_fit": 3, "growth_potential": 0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant technical experience and skills for the job requirements.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 14.057812213897705, "processed_at": "2025-07-12T07:14:02.593179", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_6", "filename": "Profile_15.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 95.0, "education_score": 70.0, "keywords_match": 97.27272727272727, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in software design and development, but limited expertise in Android development and Azure Resource Manager.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": ["Enterprise Integration Architecture", "SOA and Microservices"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.E. (Electronics Telecommunications)"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Customer focused individual", "Strong understanding of Core Java, Web Services (SOAP/REST)"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Enterprise Integration Architecture", "SOA and Microservices"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.196665048599243, "processed_at": "2025-07-12T07:14:02.598096", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_15.pdf", "file_size": 0, "word_count": 318, "success": true, "error": null}, "summary": "Customer focused individual with 12.5 years of professional experience in Software Design Development."}, {"id": "resume_7", "filename": "Profile_17.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 90.0, "education_score": 0, "keywords_match": 93.63636363636364, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills and relevant experience", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": ["Software Engineering Manager", "Senior Software Development Engineer"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Leadership experience, Technical expertise"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "HIGH", "interview_focus_areas": ["Leadership experience", "Technical expertise"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.902215957641602, "processed_at": "2025-07-12T07:14:02.601602", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_17.pdf", "file_size": 0, "word_count": 150, "success": true, "error": null}, "summary": "Senior Android Developer with expertise in distributed systems and Azure automation"}, {"id": "resume_8", "filename": "Profile_14.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 20.0, "education_score": 0, "keywords_match": 53.63636363636364, "overall_fit": 33.0, "growth_potential": 50.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate lacks required skills for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior Android Developer experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Lack of relevant work experience for Senior Android Developer role"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of relevant work experience", "Skill gaps"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.722907066345215, "processed_at": "2025-07-12T07:14:02.605604", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_14.pdf", "file_size": 0, "word_count": 218, "success": true, "error": null}, "summary": "Freelance Facilitator with experience in HR recruitment and corporate training"}]}