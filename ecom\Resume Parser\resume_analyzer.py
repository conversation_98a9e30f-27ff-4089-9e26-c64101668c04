"""
Django-Ready Resume Analyzer Interface
Simple interface for integrating resume parsing and scoring into Django applications
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

from .resume_parser import ResumeParser
from scoring_engine import ScoringEngine
from export_utils import ExportUtils

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ResumeAnalyzer:
    """
    Simple interface for resume analysis suitable for Django integration.
    Provides sequential processing without multi-threading complexity.
    """

    def __init__(self):
        """Initialize the resume analyzer with all required components"""
        self.resume_parser = ResumeParser()
        self.scoring_engine = ScoringEngine()
        self.export_utils = ExportUtils()

    def analyze_single_resume(self, resume_file_path: str, job_description: str) -> Dict[str, Any]:
        """
        Analyze a single resume file against a job description
        
        Args:
            resume_file_path (str): Path to the resume file
            job_description (str): Job description to match against
            
        Returns:
            Dict containing analysis results with score, recommendation, and details
        """
        try:
            # Parse the resume
            parsed_resume = self.resume_parser.parse_resume(resume_file_path)
            
            if not parsed_resume['success']:
                return {
                    'success': False,
                    'error': parsed_resume['error'],
                    'filename': Path(resume_file_path).name,
                    'final_score': 0,
                    'recommendation': 'REJECT'
                }
            
            # Score the resume
            scoring_result = self.scoring_engine.score_resume(parsed_resume, job_description)
            
            # Add file path information
            scoring_result['file_path'] = resume_file_path
            
            return scoring_result
            
        except Exception as e:
            logger.error(f"Error analyzing resume {resume_file_path}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'filename': Path(resume_file_path).name,
                'final_score': 0,
                'recommendation': 'REJECT',
                'file_path': resume_file_path
            }

    def analyze_resume_content(self, resume_text: str, filename: str, job_description: str) -> Dict[str, Any]:
        """
        Analyze resume content directly (useful for uploaded files in Django)
        
        Args:
            resume_text (str): Extracted resume text content
            filename (str): Original filename for reference
            job_description (str): Job description to match against
            
        Returns:
            Dict containing analysis results
        """
        try:
            # Create resume data structure
            resume_data = {
                'success': True,
                'filename': filename,
                'text': resume_text,
                'metadata': self._extract_basic_metadata(resume_text)
            }
            
            # Score the resume
            scoring_result = self.scoring_engine.score_resume(resume_data, job_description)
            
            return scoring_result
            
        except Exception as e:
            logger.error(f"Error analyzing resume content for {filename}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'filename': filename,
                'final_score': 0,
                'recommendation': 'REJECT'
            }

    def analyze_multiple_resumes(self, resume_files: List[str], job_description: str, 
                               progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        Analyze multiple resume files sequentially
        
        Args:
            resume_files (List[str]): List of resume file paths
            job_description (str): Job description to match against
            progress_callback (callable): Optional callback for progress updates
            
        Returns:
            Dict containing results and statistics
        """
        results = []
        total_files = len(resume_files)
        
        logger.info(f"Analyzing {total_files} resumes sequentially")
        
        for i, file_path in enumerate(resume_files):
            result = self.analyze_single_resume(file_path, job_description)
            results.append(result)
            
            if progress_callback:
                progress_callback(i + 1, total_files, result)
            
            logger.info(f"Processed {i + 1}/{total_files}: {Path(file_path).name}")
        
        # Calculate statistics
        statistics = self._calculate_statistics(results)
        
        return {
            'success': True,
            'results': results,
            'statistics': statistics,
            'job_description': job_description
        }

    def get_quick_score(self, resume_text: str, job_description: str) -> float:
        """
        Get a quick score for a resume (useful for API endpoints)
        
        Args:
            resume_text (str): Resume text content
            job_description (str): Job description
            
        Returns:
            float: Score between 0-100
        """
        try:
            result = self.analyze_resume_content(resume_text, "temp_resume", job_description)
            return result.get('final_score', 0)
        except Exception as e:
            logger.error(f"Error getting quick score: {str(e)}")
            return 0

    def get_recommendation(self, resume_text: str, job_description: str) -> str:
        """
        Get a quick recommendation for a resume
        
        Args:
            resume_text (str): Resume text content
            job_description (str): Job description
            
        Returns:
            str: Recommendation (HIRE, CONSIDER, REJECT)
        """
        try:
            result = self.analyze_resume_content(resume_text, "temp_resume", job_description)
            return result.get('recommendation', 'REJECT')
        except Exception as e:
            logger.error(f"Error getting recommendation: {str(e)}")
            return 'REJECT'

    def export_results_to_json(self, results: Dict[str, Any], output_path: str = None) -> Dict[str, Any]:
        """
        Export analysis results to JSON format
        
        Args:
            results (Dict): Analysis results
            output_path (str): Optional output file path
            
        Returns:
            Dict containing export status and file path
        """
        try:
            return self.export_utils.export_web_json(results, output_path)
        except Exception as e:
            logger.error(f"Error exporting results: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def _extract_basic_metadata(self, text: str) -> Dict[str, Any]:
        """Extract basic metadata from resume text"""
        import re
        
        metadata = {
            'word_count': len(text.split()),
            'has_email': bool(re.search(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)),
            'has_phone': bool(re.search(r'(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}', text)),
            'has_linkedin': 'linkedin' in text.lower(),
            'has_github': 'github' in text.lower(),
            'candidate_name': self._extract_name(text)
        }
        
        return metadata

    def _extract_name(self, text: str) -> str:
        """Simple name extraction from resume text"""
        lines = text.strip().split('\n')
        if lines:
            # Usually the first line contains the name
            first_line = lines[0].strip()
            if len(first_line.split()) >= 2 and len(first_line) < 50:
                return first_line
        return "Name not found"

    def _calculate_statistics(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate basic statistics from results"""
        total_resumes = len(results)
        successful_results = [r for r in results if r.get('success', False)]
        failed_results = [r for r in results if not r.get('success', False)]
        
        if not successful_results:
            return {
                'total_resumes': total_resumes,
                'successful': 0,
                'failed': len(failed_results),
                'average_score': 0,
                'recommendations': {'HIRE': 0, 'CONSIDER': 0, 'REJECT': total_resumes}
            }
        
        # Score statistics
        scores = [r.get('final_score', 0) for r in successful_results]
        average_score = sum(scores) / len(scores) if scores else 0
        
        # Recommendation statistics
        recommendations = {'HIRE': 0, 'CONSIDER': 0, 'REJECT': 0}
        for result in results:
            rec = result.get('recommendation', 'REJECT')
            recommendations[rec] = recommendations.get(rec, 0) + 1
        
        return {
            'total_resumes': total_resumes,
            'successful': len(successful_results),
            'failed': len(failed_results),
            'average_score': round(average_score, 2),
            'recommendations': recommendations
        }


# Example usage for Django views
def example_django_usage():
    """
    Example of how to use ResumeAnalyzer in Django views
    """
    
    # Initialize analyzer (can be done once and reused)
    analyzer = ResumeAnalyzer()
    
    # Example 1: Analyze uploaded file
    job_description = "Software Developer with Python and React experience"
    resume_file_path = "/path/to/uploaded/resume.pdf"
    
    result = analyzer.analyze_single_resume(resume_file_path, job_description)
    
    if result['success']:
        score = result['final_score']
        recommendation = result['recommendation']
        print(f"Score: {score}, Recommendation: {recommendation}")
    
    # Example 2: Quick scoring for API
    resume_text = "John Doe, Software Developer with 3 years Python experience..."
    quick_score = analyzer.get_quick_score(resume_text, job_description)
    quick_recommendation = analyzer.get_recommendation(resume_text, job_description)
    
    print(f"Quick Score: {quick_score}, Quick Recommendation: {quick_recommendation}")


if __name__ == "__main__":
    example_django_usage()
