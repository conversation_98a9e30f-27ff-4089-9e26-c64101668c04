# Resume Parser - Simplified & Clean

A streamlined resume parser for Django integration with just the essential functionality.

## 🧹 Cleaned Up Components

### Removed Files:
- ❌ `bulk_processor.py` - Replaced by simplified functions in main.py
- ❌ `README_DJANGO.md` - Redundant documentation
- ❌ `USAGE_GUIDE.md` - Redundant documentation
- ❌ `__pycache__/` - Python cache files
- ❌ `temp/` - Temporary directory

### Simplified Files:
- ✅ `main.py` - Now contains only 2 essential functions
- ✅ `django_interface.py` - Clean Django integration interface
- ✅ `config.py` - Removed threading configurations

## 📁 Current File Structure

```
Resume Parser/
├── main.py                 # Main functions: process_directory() & process_single_resume()
├── django_interface.py     # Django-ready ResumeAnalyzer class
├── config.py              # Configuration settings
├── ollama_client.py       # Ollama LLM client (sequential only)
├── scoring_engine.py      # Resume scoring logic
├── resume_parser.py       # Resume text extraction
├── export_utils.py        # Export results to JSON/CSV/Excel
├── resumes/               # Directory for resume files
└── output/                # Directory for exported results
```

## 🚀 How to Use

### Option 1: Direct Functions (Recommended)

```python
from main import process_directory, process_single_resume

# Process all resumes in a directory
results = process_directory("resumes", "Your job description")

# Process a single resume
result = process_single_resume("resume.pdf", "Your job description")
```

### Option 2: Django Interface Class

```python
from django_interface import ResumeAnalyzer

analyzer = ResumeAnalyzer()
result = analyzer.analyze_single_resume("resume.pdf", "Your job description")
```

## 🎯 Key Features

- **Sequential Processing**: No multi-threading complexity
- **Two Main Functions**: `process_directory()` and `process_single_resume()`
- **Django Ready**: Clean interface for web integration
- **Automatic Export**: Results saved to JSON files
- **Comprehensive Analysis**: Detailed scoring and recommendations

## 📊 Return Format

Both functions return comprehensive data:

```python
{
    'success': True,
    'filename': 'resume.pdf',
    'final_score': 85.5,
    'recommendation': 'HIRE',
    'analysis': {
        'candidate_name': 'John Doe',
        'skills_match': 90.0,
        'matching_skills': ['Python', 'MySQL'],
        'missing_skills': ['AWS'],
        'summary': 'Strong candidate...'
    },
    'strengths': ['Technical skills', 'Experience'],
    'weaknesses': ['Missing cloud experience'],
    'metadata': {
        'candidate_name': 'John Doe',
        'has_email': True,
        'has_phone': True
    }
}
```

## 🔧 Requirements

1. **Ollama**: Running with llama3.2:3b model
   ```bash
   ollama serve
   ollama pull llama3.2:3b
   ```

2. **Python Dependencies**: 
   ```bash
   pip install ollama pandas python-docx PyPDF2
   ```

3. **Supported Formats**: PDF, DOCX, TXT

## 🧪 Testing

Test the setup:
```python
from main import setup_environment
setup_environment()  # Returns True if everything works
```

Run your specific test:
```python
python main.py  # Processes your resume with the job description
```

## 💡 Django Integration Example

```python
# views.py
from django.http import JsonResponse
from .resume_parser.main import process_single_resume

def analyze_resume(request):
    if request.method == 'POST':
        resume_file = request.FILES['resume']
        job_description = request.POST['job_description']
        
        # Save file temporarily
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            for chunk in resume_file.chunks():
                tmp.write(chunk)
            tmp_path = tmp.name
        
        # Analyze resume
        result = process_single_resume(tmp_path, job_description)
        
        # Clean up
        os.unlink(tmp_path)
        
        return JsonResponse({
            'score': result['final_score'],
            'recommendation': result['recommendation'],
            'analysis': result['analysis']
        })
```

## 📈 Performance

- **Processing Time**: 5-15 seconds per resume
- **Memory Usage**: Low (sequential processing)
- **Scalability**: Use Celery for background processing in production

## 🎉 Benefits of Cleanup

- **Simpler**: Just 2 main functions to remember
- **Cleaner**: Removed redundant files and code
- **Faster**: No threading overhead
- **Reliable**: Sequential processing is more predictable
- **Django-Ready**: Perfect for web application integration

Your resume parser is now clean, simple, and ready for production use!
