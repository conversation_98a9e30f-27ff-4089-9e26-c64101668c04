"""
Resume Parser and Scoring System - Simple Interface
Parse and score resumes using Ollama LLM
"""

import logging
from pathlib import Path
from typing import Dict, Any

from resume_analyzer import ResumeAnalyzer
from config import RESUMES_DIR, OUTPUT_DIR, create_directories

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def process_directory(resumes_dir: str, job_description: str,
                     output_dir: str = None, min_score: float = 0,
                     verbose: bool = False) -> Dict[str, Any]:

    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Ensure directories exist
    create_directories()

    # Validate inputs
    if not job_description.strip():
        raise ValueError("Job description cannot be empty")

    if not Path(resumes_dir).exists():
        raise ValueError(f"Resumes directory does not exist: {resumes_dir}")

    # Setup output directory
    if output_dir:
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
    else:
        output_dir = OUTPUT_DIR
        Path(output_dir).mkdir(exist_ok=True)

    logger.info(f"🚀 Starting resume processing...")
    logger.info(f"📁 Resumes directory: {resumes_dir}")
    logger.info(f"📄 Job description: {job_description[:100]}...")
    logger.info(f"🔄 Processing: Sequential")

    # Progress callback
    def progress_callback(current: int, total: int, result: dict):
        filename = result.get('filename', 'unknown')
        score = result.get('final_score', 0)
        recommendation = result.get('recommendation', 'UNKNOWN')

        if result.get('success', False):
            logger.info(f"✅ [{current}/{total}] {filename} - Score: {score:.1f} - {recommendation}")
        else:
            error = result.get('error', 'Unknown error')
            logger.error(f"❌ [{current}/{total}] {filename} - Error: {error}")

    # Process resumes using ResumeAnalyzer
    analyzer = ResumeAnalyzer()

    # Find all resume files
    resume_files = []
    resumes_path = Path(resumes_dir)

    # Supported formats from config
    from config import SUPPORTED_FORMATS

    for file_path in resumes_path.rglob('*'):
        if (file_path.is_file() and file_path.suffix.lower() in SUPPORTED_FORMATS and not file_path.name.startswith('.')):  # Skip hidden files
            resume_files.append(str(file_path))

    if not resume_files:
        raise ValueError(f"No supported resume files found in {resumes_dir}")

    # Process resumes
    results = analyzer.analyze_multiple_resumes(resume_files, job_description, progress_callback)

    if not results['success']:
        raise RuntimeError(f"Processing failed: {results.get('error', 'Unknown error')}")

    # Filter results by minimum score
    if min_score > 0:
        original_count = len(results['results'])
        results['results'] = [
            r for r in results['results']
            if r.get('final_score', 0) >= min_score
        ]
        filtered_count = len(results['results'])
        logger.info(f"🔍 Filtered results: {filtered_count}/{original_count} resumes with score >= {min_score}")

    # Export results to JSON (using analyzer's built-in export)
    export_result = analyzer.export_results_to_json(results)

    if export_result['success']:
        logger.info(f"💾 Results exported to: {export_result['file_path']}")
    else:
        logger.error(f"❌ Export failed: {export_result['error']}")

    # Display summary
    stats = results['statistics']
    logger.info("\n📈 PROCESSING SUMMARY:")
    logger.info(f"   Total resumes: {stats['total_resumes']}")
    logger.info(f"   Successful: {stats['successful']}")
    logger.info(f"   Failed: {stats['failed']}")
    logger.info(f"   Average score: {stats['average_score']:.2f}")

    logger.info("\n🎯 RECOMMENDATIONS:")
    recs = stats['recommendations']
    logger.info(f"   HIRE: {recs['HIRE']}")
    logger.info(f"   CONSIDER: {recs['CONSIDER']}")
    logger.info(f"   REJECT: {recs['REJECT']}")

    logger.info("\n✅ Processing completed successfully!")

    return results


def process_single_resume(resume_file_path: str, job_description: str,
                         output_dir: str = None, verbose: bool = False) -> Dict[str, Any]:

    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Ensure directories exist
    create_directories()

    # Validate inputs
    if not job_description.strip():
        raise ValueError("Job description cannot be empty")

    if not Path(resume_file_path).exists():
        raise ValueError(f"Resume file does not exist: {resume_file_path}")

    # Setup output directory
    if output_dir:
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
    else:
        output_dir = OUTPUT_DIR
        Path(output_dir).mkdir(exist_ok=True)

    logger.info(f"🚀 Scoring resume: {Path(resume_file_path).name}")
    logger.info(f"📄 Job description: {job_description[:100]}...")

    # Process single resume using ResumeAnalyzer
    analyzer = ResumeAnalyzer()
    result = analyzer.analyze_single_resume(resume_file_path, job_description)

    if result['success']:
        score = result['final_score']
        recommendation = result['recommendation']
        logger.info(f"✅ Score: {score:.1f} - Recommendation: {recommendation}")

        # Export result to JSON (using analyzer's built-in export)
        export_data = {
            'success': True,
            'results': [result],
            'statistics': {
                'total_resumes': 1,
                'successful': 1,
                'failed': 0,
                'average_score': score,
                'recommendations': {recommendation: 1, 'HIRE': 0, 'CONSIDER': 0, 'REJECT': 0}
            },
            'job_description': job_description
        }
        export_data['statistics']['recommendations'][recommendation] = 1

        export_result = analyzer.export_results_to_json(export_data)

        if export_result['success']:
            logger.info(f"💾 Result exported to: {export_result['file_path']}")
        else:
            logger.error(f"❌ Export failed: {export_result['error']}")

        logger.info("✅ Scoring completed successfully!")
    else:
        error = result.get('error', 'Unknown error')
        logger.error(f"❌ Scoring failed: {error}")

    return result

# Simple test function
def test_resume_parser():
    """Simple test function to verify the parser works"""

    job_description = """
    Python Developer Position
    Required: Python, MySQL, Git
    Experience: Entry level to 2 years
    Location: Vijayawada, India
    """

    print("🧪 Testing Resume Parser...")

    # Test single resume processing
    try:
        result = process_single_resume(
            resume_file_path="resumes/sample_resume.pdf",
            job_description=job_description,
            verbose=True
        )

        if result['success']:
            print(f"✅ Test passed! Score: {result['final_score']}, Recommendation: {result['recommendation']}")
        else:
            print(f"❌ Test failed: {result['error']}")

    except Exception as e:
        print(f"❌ Test error: {e}")
        print("Make sure you have a resume file in the 'resumes' directory")


if __name__ == '__main__':
    # Test the resume parser with your specific job description
    
    DEFAULT_JOB_DESCRIPTION = """
        Python & MySQL Developer - Fresher
        📍 Location: Vijayawada, India
        🕒 Job Type: Full-Time | Entry-Level
        🌟 About the Role
        We're looking for a passionate and inquisitive fresher eager to kick-start their journey in backend development and data-driven applications. If you're comfortable navigating Python’s data structures and dabbling in MySQL queries, this opportunity is your perfect launchpad!

        🚀 Key Responsibilities
        Build and manage backend logic using Python with a focus on clean data structures

        Write efficient queries to interact with MySQL databases for CRUD operations

        Collaborate with frontend developers to integrate APIs and ensure seamless data flow

        Debug and optimize backend code for performance and scalability

        Document processes and assist in deployment pipelines

        🧠 Required Skills
        Solid understanding of Python fundamentals including lists, dictionaries, sets, tuples, and OOP concepts

        Familiarity with basic MySQL queries, joins, and indexing

        Exposure to version control systems like Git

        Good problem-solving and algorithmic thinking


        🤝 What We Offer
        Mentorship from senior developers

        Real-world projects to build your portfolio

        A friendly, collaborative work culture

        Opportunities for growth into full-stack development roles
        """
    
    process_single_resume("C:\\Users\\<USER>\\OneDrive\\Desktop\\resume_sasidhar.pdf",DEFAULT_JOB_DESCRIPTION)
