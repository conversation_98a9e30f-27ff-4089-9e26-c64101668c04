{"meta": {"version": "1.0", "exported_at": "2025-07-12T06:28:36.353977", "total_candidates": 23, "successful_analyses": 23, "failed_analyses": 0, "job_description": "\nSenior Android Developer - Distributed Systems & Azure Automation\nLocation: Vijayawada / Remote\nType: Full-Time\nOverview:\nWe are hiring a developer with expertise in Android development, distributed systems architecture, and cloud automation using Azure Resource Manager. The role focuses exclusively on these three technical areas, emphasizing hands-on implementation and system design.\nResponsibilities:\n- Develop Android applications using best practices\n- Design and implement scalable distributed systems\n- Automate cloud infrastructure with Azure Resource Manager\nRequired Skills:\n- Android: Strong knowledge of Kotlin/Java, Android SDK, and UI development\n- Distributed Systems: Experience with system scalability, asynchronous processing, and fault tolerance\n- Azure Resource Manager: Proficiency in ARM templates and cloud resource provisioning\n", "processed_at": "2025-07-12T06:28:36.340067"}, "summary_statistics": {"average_score": 8.49, "score_distribution": {"excellent": 0, "good": 1, "average": 1, "below_average": 21}, "recommendations": {"HIRE": 1, "CONSIDER": 0, "REJECT": 22}, "processing_time": 67.67}, "candidates": [{"id": "resume_1", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 20.0, "keywords_match": 81.81818181818181, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has relevant experience, but lacks senior Android developer skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior Android Developer role not mentioned in resume"], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science", "Bachelor of Engineering (BE), Computer Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": ["Software Development Manager experience"], "weaknesses": ["<PERSON><PERSON> of senior Android developer experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Senior Android developer experience", "Distributed systems architecture"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.074416399002075, "processed_at": "2025-07-12T06:28:35.777647", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": "Software Development Manager with 6 years of experience in Android development and distributed systems."}, {"id": "resume_2", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 5, "experience_score": 95.0, "education_score": 0, "keywords_match": 93.63636363636364, "overall_fit": 98.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills and relevant experience for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Senior Android Developer - Distributed Systems & Azure Automation"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.917354583740234, "processed_at": "2025-07-12T06:28:35.832650", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Senior Android Developer with expertise in Android development, distributed systems architecture, and cloud automation using Azure Resource Manager."}, {"id": "resume_3", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 64.4, "skills_match": 50.0, "experience_score": 100, "education_score": 0, "keywords_match": 100, "overall_fit": 88.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in Android development, distributed systems, and Azure automation.", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["Java", "Android"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure"], "skill_match_percentage": 50.0}, "experience_analysis": {"matching_experience": ["17 years in development of commercial software"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Data driven and lean startup practitioner"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 6.242599964141846, "processed_at": "2025-07-12T06:28:35.874152", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of management experience and 17 years of development experience."}, {"id": "resume_4", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 5, "education_score": 0, "keywords_match": 0, "overall_fit": 3, "growth_potential": 0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant technical experience and skills for the job requirements", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.312911748886108, "processed_at": "2025-07-12T06:28:35.894132", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_5", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 85.0, "education_score": 0, "keywords_match": 55.45454545454545, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON><PERSON> required skills for the job, but has relevant experience and growth potential.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience in Distributed Systems and Azure Resource Manager not found in resume"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.883979558944702, "processed_at": "2025-07-12T06:28:35.955085", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": ""}, {"id": "resume_6", "filename": "Profile_17.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 90.0, "education_score": 0, "keywords_match": 93.63636363636364, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in Android development and distributed systems, but limited education information.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": ["Software Engineering Manager", "Senior Software Development Engineer"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Distributed Systems", "Azure Resource Manager"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.383907794952393, "processed_at": "2025-07-12T06:28:35.965156", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_17.pdf", "file_size": 0, "word_count": 150, "success": true, "error": null}, "summary": "Senior Software Development Engineer with 9 years of experience in Android development and distributed systems."}, {"id": "resume_7", "filename": "Profile_16.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 20.0, "keywords_match": 65.45454545454545, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical expertise and leadership experience in software development", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Senior Engineer", "Manager, Software Development", "Director"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Harvard University A.B., Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Leadership experience in software development", "Technical expertise in distributed systems and Android development"], "weaknesses": ["Limited experience with Azure Resource Manager"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Leadership experience", "Technical expertise"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.52234148979187, "processed_at": "2025-07-12T06:28:35.972151", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_16.pdf", "file_size": 0, "word_count": 120, "success": true, "error": null}, "summary": "Highly experienced software developer with expertise in Android, distributed systems, and cloud automation."}, {"id": "resume_8", "filename": "Profile_15.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 95.0, "education_score": 70.0, "keywords_match": 97.27272727272727, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in distributed systems and Azure Resource Manager, but limited Android expertise.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": ["Distributed Systems", "Azure Resource Manager"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.E. (Electronics Telecommunications)"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Customer focused individual with 12.5 years of professional experience in Software Design Development"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Distributed Systems", "Azure Resource Manager"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.448232173919678, "processed_at": "2025-07-12T06:28:35.992076", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_15.pdf", "file_size": 0, "word_count": 318, "success": true, "error": null}, "summary": "Customer focused individual with 12.5 years of professional experience in Software Design Development."}, {"id": "resume_9", "filename": "Profile_19.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 95.0, "education_score": 0, "keywords_match": 99.0909090909091, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in distributed systems and cloud automation, but limited expertise in Azure Automation.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Building distributed, cloud native and server-side applications"], "weaknesses": ["Limited Spanish working proficiency"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Cloud Automation", "Distributed Systems"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.817525148391724, "processed_at": "2025-07-12T06:28:36.016079", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_19.pdf", "file_size": 0, "word_count": 546, "success": true, "error": null}, "summary": "Experienced programmer and technical lead with expertise in C++, Golang, Rust, Linux, Envoy, HTTP, TLS, multi-threaded distributed network services, container engines Docker, Kubernetes."}, {"id": "resume_10", "filename": "Profile_20.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Gaps or weaknesses in candidate's profile"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.256516933441162, "processed_at": "2025-07-12T06:28:36.044148", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_20.pdf", "file_size": 0, "word_count": 232, "success": true, "error": null}, "summary": ""}, {"id": "resume_11", "filename": "Profile_18.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 30.0, "education_score": 0, "keywords_match": 1.8181818181818183, "overall_fit": 18.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior Android Developer - Distributed Systems & Azure Automation"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.24865984916687, "processed_at": "2025-07-12T06:28:36.050159", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_18.pdf", "file_size": 0, "word_count": 118, "success": true, "error": null}, "summary": ""}, {"id": "resume_12", "filename": "Profile_14.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 20.0, "education_score": 0, "keywords_match": 53.63636363636364, "overall_fit": 33.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior Android Developer: Distributed Systems & Azure Automation"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.444441080093384, "processed_at": "2025-07-12T06:28:36.067153", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_14.pdf", "file_size": 0, "word_count": 218, "success": true, "error": null}, "summary": ""}, {"id": "resume_13", "filename": "Profile_2.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 20.0, "keywords_match": 65.45454545454545, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Candi<PERSON> has relevant skills but lacks senior experience in distributed systems and Azure automation.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior experience in distributed systems and Azure automation"], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Bachelor of Technology - BTech, Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong knowledge of Kotlin/Java, Android SDK"], "weaknesses": ["<PERSON>k of senior experience in distributed systems and Azure automation"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Distributed systems", "Azure automation"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 17.123547077178955, "processed_at": "2025-07-12T06:28:36.097074", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_2.pdf", "file_size": 0, "word_count": 119, "success": true, "error": null}, "summary": "Junior Android Developer with strong knowledge of Kotlin/Java, Android SDK, and some experience in web development."}, {"id": "resume_14", "filename": "Profile_21.pdf", "candidate_name": "Ka<PERSON>ya E.", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant technical experience and skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Easwari Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Gaps in technical skills"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills and experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.708250999450684, "processed_at": "2025-07-12T06:28:36.104071", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_21.pdf", "file_size": 0, "word_count": 169, "success": true, "error": null}, "summary": "HR Talent Acquisition professional with no direct technical experience"}, {"id": "resume_15", "filename": "Profile_3.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required technical skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Gaps in technical skills"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.338918447494507, "processed_at": "2025-07-12T06:28:36.116135", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_3.pdf", "file_size": 0, "word_count": 289, "success": true, "error": null}, "summary": ""}, {"id": "resume_16", "filename": "Profile_4.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 15.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 30.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior Android Developer - Distributed Systems & Azure Automation"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.836047410964966, "processed_at": "2025-07-12T06:28:36.141154", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_4.pdf", "file_size": 0, "word_count": 419, "success": true, "error": null}, "summary": ""}, {"id": "resume_17", "filename": "Profile_5.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 70.0, "keywords_match": 86.81818181818181, "overall_fit": 88.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in distributed systems and Azure automation, but limited Android development experience.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Distributed Systems: Experience with system scalability, asynchronous processing, and fault tolerance"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "STANDARD"}, "assessment": {"strengths": ["Leadership and mentorship", "Robust problem-solving"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Leadership and mentorship", "Distributed systems architecture"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.56422472000122, "processed_at": "2025-07-12T06:28:36.159161", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_5.pdf", "file_size": 0, "word_count": 290, "success": true, "error": null}, "summary": "Transformative journey as a Member of Technical Staff at Oracle with expertise in Apache Kafka and data processing projects."}, {"id": "resume_18", "filename": "Profile_6.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 0, "keywords_match": 95.45454545454545, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills and relevant experience, but limited Azure expertise.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Senior Software Engineer", "Samsung", "ISRO"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Technical expertise, Leadership experience"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical questions", "Leadership experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.223705768585205, "processed_at": "2025-07-12T06:28:36.182071", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_6.pdf", "file_size": 0, "word_count": 524, "success": true, "error": null}, "summary": "Senior Software Engineer with expertise in Android development, distributed systems architecture, and cloud automation using Azure Resource Manager."}, {"id": "resume_19", "filename": "Profile_9.pdf", "candidate_name": "Top Skills", "scores": {"final_score": 82.98, "skills_match": 75.0, "experience_score": 95.0, "education_score": 70.0, "keywords_match": 99.54545454545455, "overall_fit": 91.0, "growth_potential": 92.0}, "recommendation": {"decision": "HIRE", "reason": "Strong experience in Android and distributed systems, but limited Azure Resource Manager expertise.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Java", "Azure", "Android"], "missing_skills": ["<PERSON><PERSON><PERSON>"], "skill_match_percentage": 75.0}, "experience_analysis": {"matching_experience": ["Senior Member of Technical Staff at Salesforce", "Software Development Engineer at Microsoft"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Electrical, Electronics, and Communications Engineering from PSG College of Technology"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Innovation", "Scalability"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Industry experience"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 16.36788034439087, "processed_at": "2025-07-12T06:28:36.212074", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_9.pdf", "file_size": 0, "word_count": 383, "success": true, "error": null}, "summary": "Senior software engineer with expertise in Android development and distributed systems architecture."}, {"id": "resume_20", "filename": "Profile_8.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 55.0, "education_score": 20.0, "keywords_match": 79.0909090909091, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has strong knowledge of Kotlin/Java, Android development, but lacks experience with UI development and cloud resource provisioning.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Merit Certificate for Academic Excellence in Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong knowledge of Kotlin/Java, Android development"], "weaknesses": ["Lack of experience with UI development and cloud resource provisioning"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of experience with UI development", "Cloud resource provisioning"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.351884126663208, "processed_at": "2025-07-12T06:28:36.222509", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_8.pdf", "file_size": 0, "word_count": 435, "success": true, "error": null}, "summary": "Strong knowledge of Kotlin/Java, Android development, with some experience in distributed systems and Azure Resource Manager."}, {"id": "resume_21", "filename": "Profile_7.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 95.0, "education_score": 0, "keywords_match": 91.81818181818181, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON><PERSON> required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Distributed Systems", "Azure Resource Manager"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.34411382675171, "processed_at": "2025-07-12T06:28:36.274510", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_7.pdf", "file_size": 0, "word_count": 602, "success": true, "error": null}, "summary": "Summary of candidate's profile"}, {"id": "resume_22", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 40.0, "education_score": 10.0, "keywords_match": 71.81818181818181, "overall_fit": 55.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required technical skills and experience", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Android"], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology"], "education_level": "BASIC"}, "assessment": {"strengths": ["Dedicated and passionate coder with a strong desire to excel", "Technologically adept, I offer experience with various social media platforms, office technology programs, and advanced computer skills"], "weaknesses": ["Gaps in relevant work experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills and experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.042140007019043, "processed_at": "2025-07-12T06:28:36.289510", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Summary of candidate's profile"}, {"id": "resume_23", "filename": "resume_sasidhar.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 5, "experience_score": 25.0, "education_score": 0, "keywords_match": 59.09090909090909, "overall_fit": 38.0, "growth_potential": 20.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required skills for the job", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Gaps in technical expertise"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical expertise", "Relevant experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.716753721237183, "processed_at": "2025-07-12T06:28:36.335054", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume_sasidhar.pdf", "file_size": 0, "word_count": 578, "success": true, "error": null}, "summary": "Detail-oriented developer with hands-on experience in backend development and database integration"}], "top_candidates": [{"id": "resume_19", "filename": "Profile_9.pdf", "candidate_name": "Top Skills", "scores": {"final_score": 82.98, "skills_match": 75.0, "experience_score": 95.0, "education_score": 70.0, "keywords_match": 99.54545454545455, "overall_fit": 91.0, "growth_potential": 92.0}, "recommendation": {"decision": "HIRE", "reason": "Strong experience in Android and distributed systems, but limited Azure Resource Manager expertise.", "confidence": "HIGH"}, "skills_analysis": {"matching_skills": ["Java", "Azure", "Android"], "missing_skills": ["<PERSON><PERSON><PERSON>"], "skill_match_percentage": 75.0}, "experience_analysis": {"matching_experience": ["Senior Member of Technical Staff at Salesforce", "Software Development Engineer at Microsoft"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Electrical, Electronics, and Communications Engineering from PSG College of Technology"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Innovation", "Scalability"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Technical skills", "Industry experience"], "onboarding_priority": "HIGH"}, "metadata": {"processing_time": 16.36788034439087, "processed_at": "2025-07-12T06:28:36.212074", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_9.pdf", "file_size": 0, "word_count": 383, "success": true, "error": null}, "summary": "Senior software engineer with expertise in Android development and distributed systems architecture."}, {"id": "resume_3", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 64.4, "skills_match": 50.0, "experience_score": 100, "education_score": 0, "keywords_match": 100, "overall_fit": 88.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in Android development, distributed systems, and Azure automation.", "confidence": "MEDIUM"}, "skills_analysis": {"matching_skills": ["Java", "Android"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure"], "skill_match_percentage": 50.0}, "experience_analysis": {"matching_experience": ["17 years in development of commercial software"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Data driven and lean startup practitioner"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 6.242599964141846, "processed_at": "2025-07-12T06:28:35.874152", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of management experience and 17 years of development experience."}, {"id": "resume_5", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 85.0, "education_score": 0, "keywords_match": 55.45454545454545, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "<PERSON><PERSON> required skills for the job, but has relevant experience and growth potential.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience in Distributed Systems and Azure Resource Manager not found in resume"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.883979558944702, "processed_at": "2025-07-12T06:28:35.955085", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": ""}, {"id": "resume_22", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 24.0, "skills_match": 30.0, "experience_score": 40.0, "education_score": 10.0, "keywords_match": 71.81818181818181, "overall_fit": 55.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required technical skills and experience", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Android"], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology"], "education_level": "BASIC"}, "assessment": {"strengths": ["Dedicated and passionate coder with a strong desire to excel", "Technologically adept, I offer experience with various social media platforms, office technology programs, and advanced computer skills"], "weaknesses": ["Gaps in relevant work experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills and experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.042140007019043, "processed_at": "2025-07-12T06:28:36.289510", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Summary of candidate's profile"}, {"id": "resume_1", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 80.0, "education_score": 20.0, "keywords_match": 81.81818181818181, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has relevant experience, but lacks senior Android developer skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Senior Android Developer role not mentioned in resume"], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science", "Bachelor of Engineering (BE), Computer Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": ["Software Development Manager experience"], "weaknesses": ["<PERSON><PERSON> of senior Android developer experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Senior Android developer experience", "Distributed systems architecture"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.074416399002075, "processed_at": "2025-07-12T06:28:35.777647", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": "Software Development Manager with 6 years of experience in Android development and distributed systems."}, {"id": "resume_2", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 5, "experience_score": 95.0, "education_score": 0, "keywords_match": 93.63636363636364, "overall_fit": 98.0, "growth_potential": 90.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical skills and relevant experience for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Senior Android Developer - Distributed Systems & Azure Automation"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.917354583740234, "processed_at": "2025-07-12T06:28:35.832650", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Senior Android Developer with expertise in Android development, distributed systems architecture, and cloud automation using Azure Resource Manager."}, {"id": "resume_4", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 5, "education_score": 0, "keywords_match": 0, "overall_fit": 3, "growth_potential": 0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant technical experience and skills for the job requirements", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.312911748886108, "processed_at": "2025-07-12T06:28:35.894132", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_6", "filename": "Profile_17.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 90.0, "education_score": 0, "keywords_match": 93.63636363636364, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in Android development and distributed systems, but limited education information.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": ["Software Engineering Manager", "Senior Software Development Engineer"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Distributed Systems", "Azure Resource Manager"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.383907794952393, "processed_at": "2025-07-12T06:28:35.965156", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_17.pdf", "file_size": 0, "word_count": 150, "success": true, "error": null}, "summary": "Senior Software Development Engineer with 9 years of experience in Android development and distributed systems."}, {"id": "resume_7", "filename": "Profile_16.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 0, "skills_match": 0, "experience_score": 90.0, "education_score": 20.0, "keywords_match": 65.45454545454545, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong technical expertise and leadership experience in software development", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Java", "<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Senior Engineer", "Manager, Software Development", "Director"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["Harvard University A.B., Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Leadership experience in software development", "Technical expertise in distributed systems and Android development"], "weaknesses": ["Limited experience with Azure Resource Manager"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Leadership experience", "Technical expertise"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.52234148979187, "processed_at": "2025-07-12T06:28:35.972151", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_16.pdf", "file_size": 0, "word_count": 120, "success": true, "error": null}, "summary": "Highly experienced software developer with expertise in Android, distributed systems, and cloud automation."}, {"id": "resume_8", "filename": "Profile_15.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 0, "skills_match": 25.0, "experience_score": 95.0, "education_score": 70.0, "keywords_match": 97.27272727272727, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "REJECT", "reason": "Strong experience in distributed systems and Azure Resource Manager, but limited Android expertise.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Java"], "missing_skills": ["<PERSON><PERSON><PERSON>", "Azure", "Android"], "skill_match_percentage": 25.0}, "experience_analysis": {"matching_experience": ["Distributed Systems", "Azure Resource Manager"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.E. (Electronics Telecommunications)"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Customer focused individual with 12.5 years of professional experience in Software Design Development"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Distributed Systems", "Azure Resource Manager"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 11.448232173919678, "processed_at": "2025-07-12T06:28:35.992076", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_15.pdf", "file_size": 0, "word_count": 318, "success": true, "error": null}, "summary": "Customer focused individual with 12.5 years of professional experience in Software Design Development."}]}